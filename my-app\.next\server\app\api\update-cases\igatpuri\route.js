(()=>{var e={};e.id=73,e.ids=[73],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15667:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{OPTIONS:()=>u,POST:()=>l});var o=t(96559),a=t(48088),n=t(37719),i=t(32190);async function l(e){try{let r=await e.formData(),t=r.get("key");if(t&&"string"==typeof t)return console.log("Found CSV text in key field, processing for Igatpuri"),await c(t,"Igatpuri");if(t&&t instanceof File){console.log("Found file in key field:",t.name,"size:",t.size);try{let e=await t.arrayBuffer(),r=new TextDecoder("utf-8").decode(e);return await c(r,"Igatpuri")}catch(e){return console.error("Error reading key file:",e),i.NextResponse.json({error:"Failed to read uploaded file from key field"},{status:500})}}let s=r.get("file");if(s&&s instanceof File){console.log("Found file in file field:",s.name,"size:",s.size);try{let e=await s.arrayBuffer(),r=new TextDecoder("utf-8").decode(e);return await c(r,"Igatpuri")}catch(e){return console.error("Error reading file field:",e),i.NextResponse.json({error:"Failed to read uploaded file from file field"},{status:500})}}let o=r.get("csvData");if(o)return console.log("Found CSV data in csvData field"),await c(o,"Igatpuri");return console.log("No CSV data found in any field"),i.NextResponse.json({error:"No CSV file or data provided"},{status:400})}catch(e){return console.error("Error processing Igatpuri CSV:",e),i.NextResponse.json({error:"Failed to process CSV data for Igatpuri",details:e instanceof Error?e.message:"Unknown error"},{status:500,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}}async function c(e,r){try{let t=e.trim().split("\n");if(0===t.length)return i.NextResponse.json({error:"Empty CSV data"},{status:400});let s=t[0].split(",").map(e=>e.trim().replace(/"/g,"")),o=[];for(let e=1;e<t.length;e++){let a=t[e].split(",").map(e=>e.trim().replace(/"/g,""));if(a.length===s.length){let e={};s.forEach((r,t)=>{e[r]=a[t]}),e.taluka=r,o.push(e)}}return console.log(`Processed ${o.length} cases for ${r}`),i.NextResponse.json({success:!0,message:`Successfully processed ${o.length} cases for ${r}`,location:r,cases:o.slice(0,5),totalCases:o.length,headers:s},{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}catch(e){return console.error(`Error parsing CSV for ${r}:`,e),i.NextResponse.json({error:`Failed to parse CSV data for ${r}`,details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/update-cases/igatpuri/route",pathname:"/api/update-cases/igatpuri",filename:"route",bundlePath:"app/api/update-cases/igatpuri/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\api\\update-cases\\igatpuri\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:g}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(15667));module.exports=s})();