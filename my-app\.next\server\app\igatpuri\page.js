(()=>{var e={};e.id=804,e.ids=[804],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16845:(e,s,a)=>{Promise.resolve().then(a.bind(a,51119))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51119:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var r=a(60687),t=a(43210),l=a(14719),n=a(78005),i=a(40228),c=a(48730),o=a(27900),d=a(78122),m=a(28559),x=a(30382),h=a(97992),p=a(99270),u=a(80462),g=a(31158),b=a(15879),j=a(53186),N=a(24934),v=a(68988),f=a(55192),w=a(96752),C=a(63974),y=a(59821),k=a(80189),A=a(55629),S=a(37826),T=a(15616),D=a(3855);function P(){let{cases:e,loading:s,error:a,lastUpdated:P,updateCasesFromCsv:$,refreshCases:L,addCase:_}=(0,D.L)(),[E,I]=(0,t.useState)(""),[R,q]=(0,t.useState)("All Types"),[M,O]=(0,t.useState)("All Statuses"),[U,F]=(0,t.useState)(1),[z,G]=(0,t.useState)(null),[V,B]=(0,t.useState)("asc"),[W,Z]=(0,t.useState)(10),[H,X]=(0,t.useState)({}),[K,Q]=(0,t.useState)({}),[Y,J]=(0,t.useState)({}),[ee,es]=(0,t.useState)({}),[ea,er]=(0,t.useState)(""),[et,el]=(0,t.useState)(null),[en,ei]=(0,t.useState)(!1),[ec,eo]=(0,t.useState)({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),ed=(0,t.useMemo)(()=>{console.log("Total cases received:",e.length),console.log("Cases data:",e);let s=e.filter(e=>(console.log(`Case taluka: "${e.taluka}", matches Igatpuri:`,"Igatpuri"===e.taluka),"Igatpuri"===e.taluka));return console.log("Filtered Igatpuri cases:",s.length),s},[e]),em=(0,t.useMemo)(()=>["All Types",...new Set(ed.map(e=>e.caseType).filter(Boolean))],[ed]),ex=(0,t.useMemo)(()=>["All Statuses",...new Set(ed.map(e=>K[e.caseNumber]||e.received).filter(Boolean))],[ed,K]),eh=new Date,ep=(0,t.useMemo)(()=>{let e=ed.filter(e=>{let s=e.caseNumber?.toLowerCase().includes(E.toLowerCase())||e.appellant?.toLowerCase().includes(E.toLowerCase())||e.respondent?.toLowerCase().includes(E.toLowerCase())||e.caseType?.toLowerCase().includes(E.toLowerCase()),a="All Types"===R||e.caseType===R,r="All Statuses"===M||(K[e.caseNumber]||e.received)===M;return s&&a&&r});return z&&e.sort((e,s)=>{let a=e[z],r=s[z];return("date"===z&&(a=new Date(a).getTime(),r=new Date(r).getTime()),a<r)?"asc"===V?-1:1:a>r?"asc"===V?1:-1:0}),e},[ed,E,R,M,z,V]),eu=Math.ceil(ep.length/W),eg=(U-1)*W,eb=ep.slice(eg,eg+W),ej=(0,t.useMemo)(()=>{let e=ep.length;return{total:e,received:ep.filter(e=>"प्राप्त"===(K[e.caseNumber]||e.received)).length,nextDate:ep.filter(e=>""!==(Y[e.caseNumber]||e.nextDate||"").trim()).length}},[ep,K,Y]),eN=e=>{z===e?B("asc"===V?"desc":"asc"):(G(e),B("asc")),F(1)},ev=e=>{F(e)},ef=(e,s)=>{X(a=>({...a,[e]:s}))},ew=(e,s)=>{Q(a=>({...a,[e]:s}))},eC=(e,s)=>{J(a=>({...a,[e]:s}))},ey=(e,s)=>{el(e),er(H[e]||s||""),es(s=>({...s,[e]:!0}))},ek=e=>{es(s=>({...s,[e]:!1})),el(null),er("")},eA=e=>{ef(e,ea),ek(e)},eS=e=>{let s=ep.map(e=>({Date:e.date,"Case Type":e.caseType,"Case Number":e.caseNumber,Year:e.year,Appellant:e.appellant,Respondent:e.respondent,Received:K[e.caseNumber]||e.received||"-",Status:H[e.caseNumber]||e.status||""}));if("CSV"===e){let e=new Blob([[Object.keys(s[0]).join(","),...s.map(e=>Object.values(e).join(","))].join("\n")],{type:"text/csv"}),a=window.URL.createObjectURL(e),r=document.createElement("a");r.href=a,r.download=`igatpuri-cases-${new Date().toISOString().split("T")[0]}.csv`,r.click(),window.URL.revokeObjectURL(a)}else alert(`${e} export functionality would be implemented here`)},eT=e=>{let s=e?.toLowerCase()||"";if(s.includes("completed"))return{variant:"default",icon:l.A,color:"text-green-700"};if(s.includes("received"))return{variant:"secondary",icon:n.A,color:"text-orange-700"};if(s.includes("scheduled"))return{variant:"outline",icon:i.A,color:"text-amber-700"};if(s.includes("review"))return{variant:"secondary",icon:c.A,color:"text-yellow-700"};if(s.includes("issued"))return{variant:"outline",icon:o.A,color:"text-red-700"};else return{variant:"outline",icon:c.A,color:"text-gray-700"}};return s?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50/20 to-amber-50/20 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-orange-600"}),(0,r.jsx)("p",{className:"text-orange-700",children:"Loading cases..."})]})}):(0,r.jsxs)(k.Bc,{children:[(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50/20 to-amber-50/20",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl space-y-4 p-4",children:[(0,r.jsxs)("div",{className:"text-center space-y-2 pb-4 border-b bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm border-orange-100",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,r.jsxs)(N.$,{variant:"ghost",size:"sm",onClick:()=>window.location.href="/",className:"flex items-center gap-2 text-orange-700 hover:text-orange-900 hover:bg-orange-100 self-start sm:self-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Back to Home"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg",children:(0,r.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{className:"text-center sm:text-left",children:[(0,r.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-orange-900",children:"Igatpuri Legal Case Dashboard"}),(0,r.jsx)("p",{className:"text-xs sm:text-sm text-orange-700",children:"Sub-Divisional Magistrate Office, Nashik"})]})]}),(0,r.jsx)("div",{className:"hidden sm:block w-24"})," "]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-6 text-xs text-orange-600",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"Igatpuri Subdivision"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(i.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-center",children:eh.toLocaleDateString("en-IN",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4",children:[(0,r.jsx)(f.Zp,{className:"border border-orange-100 shadow-sm bg-gradient-to-br from-orange-50/50 to-white",children:(0,r.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,r.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-orange-600 mb-1",children:ej.total}),(0,r.jsx)("div",{className:"text-xs text-orange-700",children:"Total Cases"})]})}),(0,r.jsx)(f.Zp,{className:"border border-green-100 shadow-sm bg-gradient-to-br from-green-50/50 to-white",children:(0,r.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,r.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600 mb-1",children:ej.received}),(0,r.jsx)("div",{className:"text-xs text-green-700",children:"Received"})]})}),(0,r.jsx)(f.Zp,{className:"border border-blue-100 shadow-sm bg-gradient-to-br from-blue-50/50 to-white",children:(0,r.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,r.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-blue-600 mb-1",children:ej.nextDate}),(0,r.jsx)("div",{className:"text-xs text-blue-700",children:"Next Date"})]})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(f.Zp,{className:"border border-orange-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,r.jsxs)(f.Wu,{className:"p-4 space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-400 h-4 w-4"}),(0,r.jsx)(v.p,{placeholder:"Search by case number, appellant, respondent, or type...",value:E,onChange:e=>I(e.target.value),className:"pl-10 h-10 border-orange-200 focus:border-orange-500 bg-white/50"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-wrap",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,r.jsxs)(C.l6,{value:R,onValueChange:q,children:[(0,r.jsx)(C.bq,{className:"h-9 text-sm border-orange-200 bg-white/50 min-w-[120px]",children:(0,r.jsx)(C.yv,{placeholder:"Type"})}),(0,r.jsx)(C.gC,{children:em.map(e=>(0,r.jsx)(C.eb,{value:e,children:e},e))})]}),(0,r.jsxs)(C.l6,{value:M,onValueChange:O,children:[(0,r.jsx)(C.bq,{className:"h-9 text-sm border-orange-200 bg-white/50 min-w-[120px]",children:(0,r.jsx)(C.yv,{placeholder:"Status"})}),(0,r.jsx)(C.gC,{children:ex.map(e=>(0,r.jsx)(C.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,r.jsxs)(N.$,{onClick:()=>ei(!0),className:"h-9 text-sm bg-orange-600 hover:bg-orange-700 text-white",children:[(0,r.jsx)("span",{className:"text-lg mr-1",children:"+"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Add New Case"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Add"})]}),(0,r.jsxs)(N.$,{variant:"outline",onClick:()=>{I(""),q("All Types"),O("All Statuses"),F(1),G(null)},className:"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50",children:[(0,r.jsx)(u.A,{className:"h-3 w-3 mr-1"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Clear"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Clear"})]}),(0,r.jsxs)(A.rI,{children:[(0,r.jsx)(A.ty,{asChild:!0,children:(0,r.jsxs)(N.$,{variant:"outline",className:"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50",children:[(0,r.jsx)(g.A,{className:"h-3 w-3 mr-1"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Export"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Export"})]})}),(0,r.jsxs)(A.SQ,{children:[(0,r.jsx)(A._2,{onClick:()=>eS("CSV"),children:"Export as CSV"}),(0,r.jsx)(A._2,{onClick:()=>eS("PDF"),children:"Export as PDF"}),(0,r.jsx)(A._2,{onClick:()=>eS("Excel"),children:"Export as Excel"})]})]}),(0,r.jsxs)(N.$,{variant:"outline",onClick:L,disabled:s,className:"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50",children:[(0,r.jsx)(d.A,{className:`h-3 w-3 mr-1 ${s?"animate-spin":""}`}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Refresh"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Refresh"})]})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-between text-sm text-orange-700",children:(0,r.jsxs)("span",{children:["Showing ",eb.length," of ",ep.length," cases"]})})]})}),(0,r.jsx)(f.Zp,{className:"border border-orange-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,r.jsxs)(f.Wu,{className:"p-0",children:[(0,r.jsx)("div",{className:"overflow-x-auto min-w-full",children:(0,r.jsxs)(w.XI,{className:"min-w-[800px]",children:[(0,r.jsx)(w.A0,{children:(0,r.jsxs)(w.Hj,{className:"border-b border-orange-100",children:[(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[100px]",children:"Case Type"}),(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[140px]",children:(0,r.jsxs)(N.$,{variant:"ghost",onClick:()=>eN("caseNumber"),className:"h-auto p-0 font-semibold hover:bg-orange-50",children:["Case Number","caseNumber"===z&&("asc"===V?(0,r.jsx)(b.A,{className:"ml-1 h-3 w-3"}):(0,r.jsx)(j.A,{className:"ml-1 h-3 w-3"}))]})}),(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[120px]",children:"Appellant"}),(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[120px]",children:"Respondent"}),(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[80px]",children:"Received"}),(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[140px]",children:"Next Date"}),(0,r.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[120px]",children:"Status"})]})}),(0,r.jsx)(w.BF,{children:eb.map((e,s)=>(eT(e.status).icon,(0,r.jsxs)(w.Hj,{className:"hover:bg-orange-50/50",children:[(0,r.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,r.jsx)(y.E,{variant:"outline",className:"text-xs border-orange-200 text-orange-700 whitespace-nowrap",children:e.caseType})}),(0,r.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,r.jsx)("div",{className:"font-semibold text-sm break-all",children:e.caseNumber})}),(0,r.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,r.jsx)("div",{className:"font-medium text-sm break-words",children:e.appellant})}),(0,r.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,r.jsx)("div",{className:"text-sm break-words",children:e.respondent})}),(0,r.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,r.jsxs)(C.l6,{value:K[e.caseNumber]||e.received||"-",onValueChange:s=>ew(e.caseNumber,s),children:[(0,r.jsx)(C.bq,{className:"w-full min-w-[70px] h-8 text-xs border-orange-200",children:(0,r.jsx)(C.yv,{})}),(0,r.jsxs)(C.gC,{children:[(0,r.jsx)(C.eb,{value:"प्राप्त",children:"प्राप्त"}),(0,r.jsx)(C.eb,{value:"-",children:"-"})]})]})}),(0,r.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,r.jsx)(v.p,{type:"date",value:Y[e.caseNumber]||e.nextDate||"2025-07-17",onChange:s=>eC(e.caseNumber,s.target.value),className:"w-full min-w-[130px] h-8 text-xs border-orange-200 focus:border-orange-400"})}),(0,r.jsx)(w.nA,{children:(0,r.jsxs)(S.lG,{open:ee[e.caseNumber]||!1,onOpenChange:s=>{s||ek(e.caseNumber)},children:[(0,r.jsx)(S.zM,{asChild:!0,children:(0,r.jsxs)(N.$,{variant:"outline",size:"sm",className:"text-xs h-8 min-w-[120px] justify-start",onClick:()=>ey(e.caseNumber,e.status),children:[(H[e.caseNumber]||e.status||"Enter status...").substring(0,15),(H[e.caseNumber]||e.status||"").length>15?"...":""]})}),(0,r.jsxs)(S.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsx)(S.c7,{children:(0,r.jsxs)(S.L3,{children:["Edit Status - Case #",e.caseNumber]})}),(0,r.jsx)("div",{className:"grid gap-4 py-4",children:(0,r.jsx)(T.T,{value:ea,onChange:e=>er(e.target.value),placeholder:"Enter detailed status information...",className:"min-h-[100px]"})}),(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(N.$,{variant:"outline",onClick:()=>ek(e.caseNumber),children:"Cancel"}),(0,r.jsx)(N.$,{onClick:()=>eA(e.caseNumber),children:"Save"})]})]})]})})]},`${e.caseNumber}-${s}`)))})]})}),eu>1&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-3 p-4 border-t border-orange-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-sm text-orange-700",children:["Page ",U," of ",eu]}),(0,r.jsxs)(C.l6,{value:W.toString(),onValueChange:e=>{Z(Number(e)),F(1)},children:[(0,r.jsx)(C.bq,{className:"w-20 h-8 border-orange-200",children:(0,r.jsx)(C.yv,{})}),(0,r.jsxs)(C.gC,{children:[(0,r.jsx)(C.eb,{value:"5",children:"5"}),(0,r.jsx)(C.eb,{value:"10",children:"10"}),(0,r.jsx)(C.eb,{value:"20",children:"20"}),(0,r.jsx)(C.eb,{value:"50",children:"50"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(N.$,{variant:"outline",size:"sm",onClick:()=>ev(U-1),disabled:1===U,className:"border-orange-200 hover:bg-orange-50",children:"Previous"}),Array.from({length:Math.min(5,eu)},(e,s)=>{let a=Math.max(1,Math.min(eu-4,U-2))+s;return(0,r.jsx)(N.$,{variant:a===U?"default":"outline",size:"sm",onClick:()=>ev(a),className:`w-8 h-8 p-0 ${a===U?"bg-orange-600 hover:bg-orange-700":"border-orange-200 hover:bg-orange-50"}`,children:a},a)}),(0,r.jsx)(N.$,{variant:"outline",size:"sm",onClick:()=>ev(U+1),disabled:U===eu,className:"border-orange-200 hover:bg-orange-50",children:"Next"})]})]})]})})]})]})}),(0,r.jsx)(S.lG,{open:en,onOpenChange:ei,children:(0,r.jsxs)(S.Cf,{className:"sm:max-w-[500px]",children:[(0,r.jsx)(S.c7,{children:(0,r.jsx)(S.L3,{children:"Add New Case - Igatpuri"})}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)("label",{htmlFor:"caseType",className:"text-right text-sm font-medium",children:"Case Type *"}),(0,r.jsxs)(C.l6,{value:ec.caseType,onValueChange:e=>eo(s=>({...s,caseType:e})),children:[(0,r.jsx)(C.bq,{className:"col-span-3",children:(0,r.jsx)(C.yv,{})}),(0,r.jsxs)(C.gC,{children:[(0,r.jsx)(C.eb,{value:"अपील",children:"अपील"}),(0,r.jsx)(C.eb,{value:"रिव्हीजन",children:"रिव्हीजन"}),(0,r.jsx)(C.eb,{value:"मामलेदार कोर्ट",children:"मामलेदार कोर्ट"}),(0,r.jsx)(C.eb,{value:"गौणखनिज",children:"गौणखनिज"}),(0,r.jsx)(C.eb,{value:"अतिक्रमण",children:"अतिक्रमण"}),(0,r.jsx)(C.eb,{value:"कुळ कायदा",children:"कुळ कायदा"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)("label",{htmlFor:"caseNumber",className:"text-right text-sm font-medium",children:"Case Number *"}),(0,r.jsx)(v.p,{id:"caseNumber",value:ec.caseNumber,onChange:e=>eo(s=>({...s,caseNumber:e.target.value})),className:"col-span-3",placeholder:"e.g., अपील/150/2023"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)("label",{htmlFor:"appellant",className:"text-right text-sm font-medium",children:"Appellant *"}),(0,r.jsx)(v.p,{id:"appellant",value:ec.appellant,onChange:e=>eo(s=>({...s,appellant:e.target.value})),className:"col-span-3",placeholder:"Appellant name"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)("label",{htmlFor:"respondent",className:"text-right text-sm font-medium",children:"Respondent *"}),(0,r.jsx)(v.p,{id:"respondent",value:ec.respondent,onChange:e=>eo(s=>({...s,respondent:e.target.value})),className:"col-span-3",placeholder:"Respondent name"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(N.$,{variant:"outline",onClick:()=>ei(!1),children:"Cancel"}),(0,r.jsx)(N.$,{onClick:()=>{if(!ec.caseNumber||!ec.appellant||!ec.respondent){alert("Please fill in all required fields");return}let e=_({date:new Date().toISOString().split("T")[0],caseType:ec.caseType,caseNumber:ec.caseNumber,appellant:ec.appellant,respondent:ec.respondent,received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Igatpuri",filedDate:new Date().toISOString().split("T")[0],lastUpdate:new Date().toISOString().split("T")[0]});e.success?(eo({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),ei(!1),alert("Case added successfully!")):alert(`Failed to add case: ${e.error}`)},className:"bg-orange-600 hover:bg-orange-700",children:"Add Case"})]})]})})]})}function $(){return(0,r.jsx)(P,{})}},53229:(e,s,a)=>{Promise.resolve().then(a.bind(a,81703))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78228:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=a(65239),t=a(48088),l=a(88170),n=a.n(l),i=a(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let o={children:["",{children:["igatpuri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,81703)),"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\igatpuri\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\igatpuri\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/igatpuri/page",pathname:"/igatpuri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},81703:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\legal-case-dashboard\\\\my-app\\\\app\\\\igatpuri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\igatpuri\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[447,423,86,657,807],()=>a(78228));module.exports=r})();