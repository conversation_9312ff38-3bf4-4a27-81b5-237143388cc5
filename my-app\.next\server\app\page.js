(()=>{var e={};e.id=974,e.ids=[974],e.modules={2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],l=r[0];if(Array.isArray(n)&&Array.isArray(l)){if(n[0]!==l[0]||n[2]!==l[2])return!0}else if(n!==l)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],o=Object.values(r[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return l}});let n=r(19169);function l(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return s}});let n=r(51550),l=r(59656);var a=l._("_maxConcurrency"),o=l._("_runningCount"),u=l._("_queue"),i=l._("_processNext");class s{enqueue(e){let t,r;let l=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,o)[o]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,o)[o]--,n._(this,i)[i]()}};return n._(this,u)[u].push({promiseFn:l,task:a}),n._(this,i)[i](),l}bump(e){let t=n._(this,u)[u].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,u)[u].splice(t,1)[0];n._(this,u)[u].unshift(e),n._(this,i)[i](!0)}}constructor(e=5){Object.defineProperty(this,i,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,u,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,o)[o]=0,n._(this,u)[u]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,o)[o]<n._(this,a)[a]||e)&&n._(this,u)[u].length>0){var t;null==(t=n._(this,u)[u].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return s},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return d}});let n=r(59008),l=r(59154),a=r(75076);function o(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function u(e,t,r){return o(e,t===l.PrefetchKind.FULL,r)}function i(e){let{url:t,nextUrl:r,tree:n,prefetchCache:a,kind:u,allowAliasing:i=!0}=e,s=function(e,t,r,n,a){for(let u of(void 0===t&&(t=l.PrefetchKind.TEMPORARY),[r,null])){let r=o(e,!0,u),i=o(e,!1,u),s=e.search?r:i,c=n.get(s);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(i);if(a&&e.search&&t!==l.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==l.PrefetchKind.FULL&&a){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,u,r,a,i);return s?(s.status=h(s),s.kind!==l.PrefetchKind.FULL&&u===l.PrefetchKind.FULL&&s.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:null!=u?u:l.PrefetchKind.TEMPORARY})}),u&&s.kind===l.PrefetchKind.TEMPORARY&&(s.kind=u),s):c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:u||l.PrefetchKind.TEMPORARY})}function s(e){let{nextUrl:t,tree:r,prefetchCache:n,url:a,data:o,kind:i}=e,s=o.couldBeIntercepted?u(a,i,t):u(a,i),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(o),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:s,status:l.PrefetchCacheEntryStatus.fresh,url:a};return n.set(s,c),c}function c(e){let{url:t,kind:r,tree:o,nextUrl:i,prefetchCache:s}=e,c=u(t,r),d=a.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:o,nextUrl:i,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:l}=e,a=n.get(l);if(!a)return;let o=u(t,a.kind,r);return n.set(o,{...a,key:o}),n.delete(l),o}({url:t,existingCacheKey:c,nextUrl:i,prefetchCache:s})),e.prerendered){let t=s.get(null!=r?r:c);t&&(t.kind=l.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:o,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:l.PrefetchCacheEntryStatus.fresh,url:t};return s.set(c,f),f}function d(e){for(let[t,r]of e)h(r)===l.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:a}=e;return -1!==a?Date.now()<r+a?l.PrefetchCacheEntryStatus.fresh:l.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.fresh:t===l.PrefetchKind.AUTO&&Date.now()<r+p?l.PrefetchCacheEntryStatus.stale:t===l.PrefetchKind.FULL&&Date.now()<r+p?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let n=r(96127);function l(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(59154),r(25232),r(29651),r(28627),r(78866),r(75076),r(97936),r(35429);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(83913),l=r(89752),a=r(86770),o=r(57391),u=r(33123),i=r(33898),s=r(59435);function c(e,t,r,c){let f,p=e.tree,h=e.cache,g=(0,o.createHrefFromUrl)(r);if("string"==typeof t)return!1;for(let e of t){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(e.seedData))continue;let t=e.tree;t=d(t,Object.fromEntries(r.searchParams));let{seedData:o,isRootRender:s,pathToSegment:c}=e,y=["",...c];t=d(t,Object.fromEntries(r.searchParams));let m=(0,a.applyRouterStatePatchToTree)(y,p,t,g),b=(0,l.createEmptyCacheNode)();if(s&&o){let e=o[1];b.loading=o[3],b.rsc=e,function e(t,r,l,a){if(0!==Object.keys(l[1]).length)for(let o in l[1]){let i;let s=l[1][o],c=s[0],d=(0,u.createRouterCacheKey)(c),f=null!==a&&void 0!==a[2][o]?a[2][o]:null;if(null!==f){let e=f[1],t=f[3];i={lazyData:null,rsc:c.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else i={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let p=t.parallelRoutes.get(o);p?p.set(d,i):t.parallelRoutes.set(o,new Map([[d,i]])),e(i,r,s,f)}}(b,h,t,o)}else b.rsc=h.rsc,b.prefetchRsc=h.prefetchRsc,b.loading=h.loading,b.parallelRoutes=new Map(h.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(b,h,e);m&&(p=m,h=b,f=!0)}return!!f&&(c.patchedTree=p,c.cache=h,c.canonicalUrl=g,c.hashFragment=r.hash,(0,s.handleMutable)(e,c))}function d(e,t){let[r,l,...a]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),l,...a];let o={};for(let[e,r]of Object.entries(l))o[e]=d(r,t);return[r,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[u,i]=a,s=(0,n.createRouterCacheKey)(i),c=r.parallelRoutes.get(u);if(!c)return;let d=t.parallelRoutes.get(u);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(u,d)),o){d.delete(s);return}let f=c.get(s),p=d.get(s);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(s,p)),e(p,f,(0,l.getNextFlightSegmentPath)(a)))}}});let n=r(33123),l=r(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},22308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,l,,o]=t;for(let u in n.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=r,t[3]="refresh"),l)e(l[u],r)}},refreshInactiveParallelSegments:function(){return o}});let n=r(56928),l=r(59008),a=r(83913);async function o(e){let t=new Set;await u({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function u(e){let{state:t,updatedTree:r,updatedCache:a,includeNextUrl:o,fetchedSegments:i,rootTree:s=r,canonicalUrl:c}=e,[,d,f,p]=r,h=[];if(f&&f!==c&&"refresh"===p&&!i.has(f)){i.add(f);let e=(0,l.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:o?t.nextUrl:null}).then(e=>{let{flightData:t}=e;if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(a,a,e)});h.push(e)}for(let e in d){let r=u({state:t,updatedTree:d[e],updatedCache:a,includeNextUrl:o,fetchedSegments:i,rootTree:s,canonicalUrl:c});h.push(r)}await Promise.all(h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(60687);r(43210);var l=r(8730),a=r(24224),o=r(96241);let u=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:e,variant:t,size:r,asChild:a=!1,...i}){let s=a?l.DX:"button";return(0,n.jsx)(s,{"data-slot":"button",className:(0,o.cn)(u({variant:t,size:r,className:e})),...i})}},25232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return v},navigateReducer:function(){return function e(t,r){let{url:P,isExternalUrl:x,navigateType:R,shouldScroll:j,allowAliasing:w}=r,O={},{hash:E}=P,T=(0,l.createHrefFromUrl)(P),M="push"===R;if((0,y.prunePrefetchCache)(t.prefetchCache),O.preserveCustomHistoryState=!1,O.pendingPush=M,x)return v(t,O,P.toString(),M);if(document.getElementById("__next-page-redirect"))return v(t,O,T,M);let S=(0,y.getOrCreatePrefetchCacheEntry)({url:P,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:w}),{treeAtTimeOfPrefetch:N,data:C}=S;return f.prefetchQueue.bump(C),C.then(f=>{let{flightData:y,canonicalUrl:x,postponed:R}=f,w=!1;if(S.lastUsedTime||(S.lastUsedTime=Date.now(),w=!0),S.aliased){let n=(0,b.handleAliasedPrefetchEntry)(t,y,P,O);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof y)return v(t,O,y,M);let C=x?(0,l.createHrefFromUrl)(x):T;if(E&&t.canonicalUrl.split("#",1)[0]===C.split("#",1)[0])return O.onlyHashChange=!0,O.canonicalUrl=C,O.shouldScroll=j,O.hashFragment=E,O.scrollableSegments=[],(0,c.handleMutable)(t,O);let A=t.tree,U=t.cache,k=[];for(let e of y){let{pathToSegment:r,seedData:l,head:c,isHeadPartial:f,isRootRender:y}=e,b=e.tree,x=["",...r],j=(0,o.applyRouterStatePatchToTree)(x,A,b,T);if(null===j&&(j=(0,o.applyRouterStatePatchToTree)(x,N,b,T)),null!==j){if(l&&y&&R){let e=(0,g.startPPRNavigation)(U,A,b,l,c,f,!1,k);if(null!==e){if(null===e.route)return v(t,O,T,M);j=e.route;let r=e.node;null!==r&&(O.cache=r);let l=e.dynamicRequestTree;if(null!==l){let r=(0,n.fetchServerResponse)(P,{flightRouterState:l,nextUrl:t.nextUrl});(0,g.listenForDynamicRequest)(e,r)}}else j=b}else{if((0,i.isNavigatingToNewRootLayout)(A,j))return v(t,O,T,M);let n=(0,p.createEmptyCacheNode)(),l=!1;for(let t of(S.status!==s.PrefetchCacheEntryStatus.stale||w?l=(0,d.applyFlightData)(U,n,e,S):(l=function(e,t,r,n){let l=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),_(n).map(e=>[...r,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,a),l=!0;return l}(n,U,r,b),S.lastUsedTime=Date.now()),(0,u.shouldHardNavigate)(x,A)?(n.rsc=U.rsc,n.prefetchRsc=U.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(n,U,r),O.cache=n):l&&(O.cache=n,U=n),_(b))){let e=[...r,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&k.push(e)}}A=j}}return O.patchedTree=A,O.canonicalUrl=C,O.scrollableSegments=k,O.hashFragment=E,O.shouldScroll=j,(0,c.handleMutable)(t,O)},()=>t)}}});let n=r(59008),l=r(57391),a=r(18468),o=r(86770),u=r(65951),i=r(2030),s=r(59154),c=r(59435),d=r(56928),f=r(75076),p=r(89752),h=r(83913),g=r(65956),y=r(5334),m=r(97464),b=r(9707);function v(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function _(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,l]of Object.entries(n))for(let n of _(l))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return l}});let n=r(2255);function l(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(57391),l=r(70642);function a(e,t){var r;let{url:a,tree:o}=t,u=(0,n.createHrefFromUrl)(a),i=o||e.tree,s=e.cache;return{canonicalUrl:u,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:s,prefetchCache:e.prefetchCache,tree:i,nextUrl:null!=(r=(0,l.extractPathFromFlightRouterState)(i))?r:a.pathname}}r(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(57391),l=r(86770),a=r(2030),o=r(25232),u=r(56928),i=r(59435),s=r(89752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c}}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof r)return(0,o.handleExternalUrl)(e,d,r,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:i}=t,h=(0,l.applyRouterStatePatchToTree)(["",...r],f,i,e.canonicalUrl);if(null===h)return e;if((0,a.isNavigatingToNewRootLayout)(f,h))return(0,o.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,n.createHrefFromUrl)(c):void 0;g&&(d.canonicalUrl=g);let y=(0,s.createEmptyCacheNode)();(0,u.applyFlightData)(p,y,t),d.patchedTree=h,d.cache=y,p=y,f=h}return(0,i.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return u},urlObjectKeys:function(){return o}});let n=r(40740)._(r(76715)),l=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",o=e.pathname||"",u=e.hash||"",i=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||l.test(a))&&!1!==s?(s="//"+(s||""),o&&"/"!==o[0]&&(o="/"+o)):s||(s=""),u&&"#"!==u[0]&&(u="#"+u),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+u}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return a(e)}},33873:e=>{"use strict";e.exports=require("path")},33898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return s}});let n=r(34400),l=r(41500),a=r(33123),o=r(83913);function u(e,t,r,u,i){let{segmentPath:s,seedData:c,tree:d,head:f}=r,p=e,h=t;for(let e=0;e<s.length;e+=2){let t=s[e],r=s[e+1],g=e===s.length-2,y=(0,a.createRouterCacheKey)(r),m=h.parallelRoutes.get(t);if(!m)continue;let b=p.parallelRoutes.get(t);b&&b!==m||(b=new Map(m),p.parallelRoutes.set(t,b));let v=m.get(y),_=b.get(y);if(g){if(c&&(!_||!_.lazyData||_===v)){let e=c[0],t=c[1],r=c[3];_={lazyData:null,rsc:i||e!==o.PAGE_SEGMENT_KEY?t:null,prefetchRsc:null,head:null,prefetchHead:null,loading:r,parallelRoutes:i&&v?new Map(v.parallelRoutes):new Map},v&&i&&(0,n.invalidateCacheByRouterState)(_,v,d),i&&(0,l.fillLazyItemsTillLeafWithHead)(_,v,d,c,f,u),b.set(y,_)}continue}_&&v&&(_===v&&(_={lazyData:_.lazyData,rsc:_.rsc,prefetchRsc:_.prefetchRsc,head:_.head,prefetchHead:_.prefetchHead,parallelRoutes:new Map(_.parallelRoutes),loading:_.loading},b.set(y,_)),p=_,h=v)}}function i(e,t,r,n){u(e,t,r,n,!0)}function s(e,t,r,n){u(e,t,r,n,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let n=r(33123);function l(e,t,r){for(let l in r[1]){let a=r[1][l][0],o=(0,n.createRouterCacheKey)(a),u=t.parallelRoutes.get(l);if(u){let t=new Map(u);t.delete(o),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return i},isBot:function(){return u}});let n=r(95796),l=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function o(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function u(e){return l.test(e)||o(e)}function i(e){return l.test(e)?"dom":o(e)?"html":void 0}},35429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let n=r(11264),l=r(11448),a=r(91563),o=r(59154),u=r(6361),i=r(57391),s=r(25232),c=r(86770),d=r(2030),f=r(59435),p=r(41500),h=r(89752),g=r(68214),y=r(96493),m=r(22308),b=r(74007),v=r(36875),_=r(97860),P=r(5334),x=r(25942),R=r(26736),j=r(24642);r(50593);let{createFromFetch:w,createTemporaryReferenceSet:O,encodeReply:E}=r(19357);async function T(e,t,r){let o,i,{actionId:s,actionArgs:c}=r,d=O(),f=(0,j.extractInfoFromServerReferenceId)(s),p="use-cache"===f.type?(0,j.omitUnusedArgs)(c,f):c,h=await E(p,{temporaryReferences:d}),g=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:s,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[a.NEXT_URL]:t}:{}},body:h}),y=g.headers.get("x-action-redirect"),[m,v]=(null==y?void 0:y.split(";"))||[];switch(v){case"push":o=_.RedirectType.push;break;case"replace":o=_.RedirectType.replace;break;default:o=void 0}let P=!!g.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(g.headers.get("x-action-revalidated")||"[[],0,0]");i={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){i={paths:[],tag:!1,cookie:!1}}let x=m?(0,u.assignLocation)(m,new URL(e.canonicalUrl,window.location.href)):void 0,R=g.headers.get("content-type");if(null==R?void 0:R.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await w(Promise.resolve(g),{callServer:n.callServer,findSourceMapURL:l.findSourceMapURL,temporaryReferences:d});return m?{actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:x,redirectType:o,revalidatedParts:i,isPrerender:P}:{actionResult:e.a,actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:x,redirectType:o,revalidatedParts:i,isPrerender:P}}if(g.status>=400)throw Object.defineProperty(Error("text/plain"===R?await g.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:x,redirectType:o,revalidatedParts:i,isPrerender:P}}function M(e,t){let{resolve:r,reject:n}=t,l={},a=e.tree;l.preserveCustomHistoryState=!1;let u=e.nextUrl&&(0,g.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return T(e,u,t).then(async g=>{let b,{actionResult:j,actionFlightData:w,redirectLocation:O,redirectType:E,isPrerender:T,revalidatedParts:M}=g;if(O&&(E===_.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=b=(0,i.createHrefFromUrl)(O,!1)),!w)return(r(j),O)?(0,s.handleExternalUrl)(e,l,O.href,e.pushRef.pendingPush):e;if("string"==typeof w)return r(j),(0,s.handleExternalUrl)(e,l,w,e.pushRef.pendingPush);let S=M.paths.length>0||M.tag||M.cookie;for(let n of w){let{tree:o,seedData:i,head:f,isRootRender:g}=n;if(!g)return console.log("SERVER ACTION APPLY FAILED"),r(j),e;let v=(0,c.applyRouterStatePatchToTree)([""],a,o,b||e.canonicalUrl);if(null===v)return r(j),(0,y.handleSegmentMismatch)(e,t,o);if((0,d.isNavigatingToNewRootLayout)(a,v))return r(j),(0,s.handleExternalUrl)(e,l,b||e.canonicalUrl,e.pushRef.pendingPush);if(null!==i){let t=i[1],r=(0,h.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=i[3],(0,p.fillLazyItemsTillLeafWithHead)(r,void 0,o,i,f,void 0),l.cache=r,l.prefetchCache=new Map,S&&await (0,m.refreshInactiveParallelSegments)({state:e,updatedTree:v,updatedCache:r,includeNextUrl:!!u,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=v,a=v}return O&&b?(S||((0,P.createSeededPrefetchCacheEntry)({url:O,data:{flightData:w,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:T?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),n((0,v.getRedirectError)((0,R.hasBasePath)(b)?(0,x.removeBasePath)(b):b,E||_.RedirectType.push))):r(j),(0,f.handleMutable)(e,l)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38202:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return s},getCurrentAppRouterState:function(){return c}});let n=r(59154),l=r(8830),a=r(43210),o=r(91992);function u(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?i({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function i(e){let{actionQueue:t,action:r,setState:n}=e,l=t.state;t.pending=r;let a=r.payload,i=t.action(l,a);function s(e){!r.discarded&&(t.state=e,u(t,n),r.resolve(e))}(0,o.isThenable)(i)?i.then(s,e=>{u(t,n),r.reject(e)}):s(i)}function s(e){let t={state:e,dispatch:(e,r)=>(function(e,t,r){let l={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=o,i({actionQueue:e,action:o,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),i({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(t,e,r),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null};return t}function c(){return null}},41500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,o,u,i){if(0===Object.keys(a[1]).length){t.head=u;return}for(let s in a[1]){let c;let d=a[1][s],f=d[0],p=(0,n.createRouterCacheKey)(f),h=null!==o&&void 0!==o[2][s]?o[2][s]:null;if(r){let n=r.parallelRoutes.get(s);if(n){let r;let a=(null==i?void 0:i.kind)==="auto"&&i.status===l.PrefetchCacheEntryStatus.reusable,o=new Map(n),c=o.get(p);r=null!==h?{lazyData:null,rsc:h[1],prefetchRsc:null,head:null,prefetchHead:null,loading:h[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes)}:a&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),loading:null},o.set(p,r),e(r,c,d,h||null,u,i),t.parallelRoutes.set(s,o);continue}}if(null!==h){let e=h[1],t=h[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let g=t.parallelRoutes.get(s);g?g.set(p,c):t.parallelRoutes.set(s,new Map([[p,c]])),e(c,void 0,d,h,u,i)}}}});let n=r(33123),l=r(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42626:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>s});var n=r(65239),l=r(48088),a=r(88170),o=r.n(a),u=r(30893),i={};for(let e in u)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>u[e]);r.d(t,i);let s={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90597)),"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},44397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let n=r(33123);function l(e,t){return function e(t,r,l){if(0===Object.keys(r).length)return[t,l];if(r.children){let[a,o]=r.children,u=t.parallelRoutes.get("children");if(u){let t=(0,n.createRouterCacheKey)(a),r=u.get(t);if(r){let n=e(r,o,l+"/"+t);if(n)return n}}}for(let a in r){if("children"===a)continue;let[o,u]=r[a],i=t.parallelRoutes.get(a);if(!i)continue;let s=(0,n.createRouterCacheKey)(o),c=i.get(s);if(!c)continue;let d=e(c,u,l+"/"+s);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45987:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var n=r(60687),l=r(85814),a=r.n(l),o=r(55192),u=r(24934),i=r(30382),s=r(62688);let c=(0,s.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),d=(0,s.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var f=r(48730);let p=(0,s.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var h=r(97992);let g=(0,s.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function y(){return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:(0,n.jsxs)("div",{className:"mx-auto max-w-6xl space-y-8 p-4 sm:p-6 lg:p-8",children:[(0,n.jsx)("div",{className:"text-center space-y-6 py-8 sm:py-12",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4 mb-6",children:[(0,n.jsx)("div",{className:"p-4 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl shadow-lg",children:(0,n.jsx)(i.A,{className:"h-10 w-10 sm:h-12 sm:w-12 text-white"})}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Legal Case Monitoring System"}),(0,n.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 font-medium",children:"Sub-Divisional Magistrate Office, Nashik"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-500 max-w-2xl mx-auto",children:"Streamlined case management and monitoring for efficient legal proceedings across subdivisions"})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 mb-8",children:[(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(c,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Case Management"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(d,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Multi-User Access"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(f.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-orange-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Real-time Updates"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(p,{className:"h-6 w-6 sm:h-8 sm:w-8 text-purple-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Secure Platform"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8",children:[(0,n.jsxs)(o.Zp,{className:"group border border-orange-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-orange-50 to-amber-50",children:[(0,n.jsxs)(o.aR,{className:"text-center pb-6",children:[(0,n.jsx)("div",{className:"p-4 bg-gradient-to-br from-orange-500 to-amber-500 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,n.jsx)(h.A,{className:"h-8 w-8 text-white"})}),(0,n.jsx)(o.ZB,{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",children:"Igatpuri Subdivision"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-600 leading-relaxed",children:"Monitor and manage legal cases for Igatpuri area with comprehensive tracking and reporting"})]}),(0,n.jsx)(o.Wu,{className:"pt-0 pb-6",children:(0,n.jsx)(a(),{href:"/igatpuri",className:"block",children:(0,n.jsxs)(u.$,{className:"w-full bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",children:["Access Igatpuri Dashboard",(0,n.jsx)(g,{className:"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"})]})})})]}),(0,n.jsxs)(o.Zp,{className:"group border border-indigo-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-indigo-50 to-purple-50",children:[(0,n.jsxs)(o.aR,{className:"text-center pb-6",children:[(0,n.jsx)("div",{className:"p-4 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,n.jsx)(h.A,{className:"h-8 w-8 text-white"})}),(0,n.jsx)(o.ZB,{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",children:"Trimbakeshwar Subdivision"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-600 leading-relaxed",children:"Monitor and manage legal cases for Trimbakeshwar area with comprehensive tracking and reporting"})]}),(0,n.jsx)(o.Wu,{className:"pt-0 pb-6",children:(0,n.jsx)(a(),{href:"/trimbakeshwar",className:"block",children:(0,n.jsxs)(u.$,{className:"w-full bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",children:["Access Trimbakeshwar Dashboard",(0,n.jsx)(g,{className:"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"})]})})})]})]}),(0,n.jsxs)("div",{className:"text-center py-8 border-t border-gray-200 mt-12",children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"\xa9 2024 Sub-Divisional Magistrate Office, Nashik. All rights reserved."}),(0,n.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Powered by Legal Case Monitoring System"})]})]})})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(31658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},50546:(e,t,r)=>{Promise.resolve().then(r.bind(r,45987))},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},bumpPrefetchTask:function(){return s},cancelPrefetchTask:function(){return i},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return o},navigate:function(){return l},prefetch:function(){return n},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return u}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,l=r,a=r,o=r,u=r,i=r,s=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},53038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let n=r(43210);function l(e,t){let r=(0,n.useRef)(null),l=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(r.current=a(e,n)),t&&(l.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(84949),l=r(19169),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,l.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55192:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,ZB:()=>u,Zp:()=>a,aR:()=>o});var n=r(60687);r(43210);var l=r(96241);function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function u({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",e),...t})}},56928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(41500),l=r(33898);function a(e,t,r,a){let{tree:o,seedData:u,head:i,isRootRender:s}=r;if(null===u)return!1;if(s){let r=u[1];t.loading=u[3],t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,o,u,i,a)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,l.fillCacheWithNewSubTreeData)(t,e,r,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57683:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>i});var n=r(37413),l=r(260),a=r.n(l),o=r(73298),u=r.n(o);r(82704);let i={title:"Create Next App",description:"Generated by create next app"};function s({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().variable} ${u().variable} antialiased`,children:e})})}},58793:()=>{},59435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(70642);function l(e){return void 0!==e}function a(e,t){var r,a;let o=null==(r=t.shouldScroll)||r,u=e.nextUrl;if(l(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?u=r:u||(u=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>l});var n=0;function l(e){return"__private_"+n+++"_"+e}},61520:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useSyncDevRenderIndicator",{enumerable:!0,get:function(){return n}});let r=e=>e(),n=()=>r;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[a,o]=r,[u,i]=t;return(0,l.matchSegment)(u,a)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),o[i]):!!Array.isArray(u)}}});let n=r(74007),l=r(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return i},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],l=t.parallelRoutes,o=new Map(l);for(let t in n){let r=n[t],u=r[0],i=(0,a.createRouterCacheKey)(u),s=l.get(t);if(void 0!==s){let n=s.get(i);if(void 0!==n){let l=e(n,r),a=new Map(s);a.set(i,l),o.set(t,a)}}}let u=t.rsc,i=y(u)&&"pending"===u.status;return{lazyData:null,rsc:u,head:t.head,prefetchHead:i?t.prefetchHead:[null,null],prefetchRsc:i?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o}}}});let n=r(83913),l=r(14077),a=r(33123),o=r(2030),u={route:null,node:null,dynamicRequestTree:null,children:null};function i(e,t,r,o,i,d,f,p){return function e(t,r,o,i,d,f,p,h,g,y){let m=r[1],b=o[1],v=null!==d?d[2]:null;i||!0!==o[4]||(i=!0);let _=t.parallelRoutes,P=new Map(_),x={},R=null,j=!1,w={};for(let t in b){let r;let o=b[t],c=m[t],d=_.get(t),O=null!==v?v[t]:null,E=o[0],T=g.concat([t,E]),M=(0,a.createRouterCacheKey)(E),S=void 0!==c?c[0]:void 0,N=void 0!==d?d.get(M):void 0;if(null!==(r=E===n.DEFAULT_SEGMENT_KEY?void 0!==c?{route:c,node:null,dynamicRequestTree:null,children:null}:s(c,o,i,void 0!==O?O:null,f,p,T,y):h&&0===Object.keys(o[1]).length?s(c,o,i,void 0!==O?O:null,f,p,T,y):void 0!==c&&void 0!==S&&(0,l.matchSegment)(E,S)&&void 0!==N&&void 0!==c?e(N,c,o,i,O,f,p,h,T,y):s(c,o,i,void 0!==O?O:null,f,p,T,y))){if(null===r.route)return u;null===R&&(R=new Map),R.set(t,r);let e=r.node;if(null!==e){let r=new Map(d);r.set(M,e),P.set(t,r)}let n=r.route;x[t]=n;let l=r.dynamicRequestTree;null!==l?(j=!0,w[t]=l):w[t]=n}else x[t]=o,w[t]=o}if(null===R)return null;let O={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:P};return{route:c(o,x),node:O,dynamicRequestTree:j?c(o,w):null,children:R}}(e,t,r,!1,o,i,d,f,[],p)}function s(e,t,r,n,l,i,s,f){return!r&&(void 0===e||(0,o.isNavigatingToNewRootLayout)(e,t))?u:function e(t,r,n,l,o,u){if(null===r)return d(t,null,n,l,o,u);let i=t[1],s=r[4],f=0===Object.keys(i).length;if(s||l&&f)return d(t,r,n,l,o,u);let p=r[2],h=new Map,g=new Map,y={},m=!1;if(f)u.push(o);else for(let t in i){let r=i[t],s=null!==p?p[t]:null,c=r[0],d=o.concat([t,c]),f=(0,a.createRouterCacheKey)(c),b=e(r,s,n,l,d,u);h.set(t,b);let v=b.dynamicRequestTree;null!==v?(m=!0,y[t]=v):y[t]=r;let _=b.node;if(null!==_){let e=new Map;e.set(f,_),g.set(t,e)}}return{route:t,node:{lazyData:null,rsc:r[1],prefetchRsc:null,head:f?n:null,prefetchHead:null,loading:r[3],parallelRoutes:g},dynamicRequestTree:m?c(t,y):null,children:h}}(t,n,l,i,s,f)}function c(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function d(e,t,r,n,l,o){let u=c(e,e[1]);return u[3]="refetch",{route:e,node:function e(t,r,n,l,o,u){let i=t[1],s=null!==r?r[2]:null,c=new Map;for(let t in i){let r=i[t],d=null!==s?s[t]:null,f=r[0],p=o.concat([t,f]),h=(0,a.createRouterCacheKey)(f),g=e(r,void 0===d?null:d,n,l,p,u),y=new Map;y.set(h,g),c.set(t,y)}let d=0===c.size;d&&u.push(o);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:c,prefetchRsc:void 0!==f?f:null,prefetchHead:d?n:[null,null],loading:void 0!==p?p:null,rsc:m(),head:d?m():null}}(e,t,r,n,l,o),dynamicRequestTree:u,children:null}}function f(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:o,head:u}=t;o&&function(e,t,r,n,o){let u=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=u.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(n,t)){u=e;continue}}}return}(function e(t,r,n,o){if(null===t.dynamicRequestTree)return;let u=t.children,i=t.node;if(null===u){null!==i&&(function e(t,r,n,o,u){let i=r[1],s=n[1],c=o[2],d=t.parallelRoutes;for(let t in i){let r=i[t],n=s[t],o=c[t],f=d.get(t),p=r[0],g=(0,a.createRouterCacheKey)(p),y=void 0!==f?f.get(g):void 0;void 0!==y&&(void 0!==n&&(0,l.matchSegment)(p,n[0])&&null!=o?e(y,r,n,o,u):h(r,y,null))}let f=t.rsc,p=o[1];null===f?t.rsc=p:y(f)&&f.resolve(p);let g=t.head;y(g)&&g.resolve(u)}(i,t.route,r,n,o),t.dynamicRequestTree=null);return}let s=r[1],c=n[2];for(let t in r){let r=s[t],n=c[t],a=u.get(t);if(void 0!==a){let t=a.route[0];if((0,l.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,o)}}})(u,r,n,o)}(e,r,n,o,u)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)h(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function h(e,t,r){let n=e[1],l=t.parallelRoutes;for(let e in n){let t=n[e],o=l.get(e);if(void 0===o)continue;let u=t[0],i=(0,a.createRouterCacheKey)(u),s=o.get(i);void 0!==s&&h(t,s,r)}let o=t.rsc;y(o)&&(null===r?o.resolve(null):o.reject(r));let u=t.head;y(u)&&u.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function m(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=g,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return s},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],a=Array.isArray(t),o=a?t[1]:t;!(!o||o.startsWith(l.PAGE_SEGMENT_KEY))&&(a&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):a&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(72859),l=r(83913),a=r(14077),o=e=>"/"===e[0]?e.slice(1):e,u=e=>"string"==typeof e?"children"===e?"":e:e[1];function i(e){return e.reduce((e,t)=>""===(t=o(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function s(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===l.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(l.PAGE_SEGMENT_KEY))return"";let a=[u(r)],o=null!=(t=e[1])?t:{},c=o.children?s(o.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let r=s(t);void 0!==r&&a.push(r)}return i(a)}function c(e,t){let r=function e(t,r){let[l,o]=t,[i,c]=r,d=u(l),f=u(i);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(l,i)){var p;return null!=(p=s(r))?p:""}for(let t in o)if(c[t]){let r=e(o[t],c[t]);if(null!==r)return u(i)+"/"+r}return null}(e,t);return null==r||"/"===r?r:i(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{mountLinkInstance:function(){return s},onLinkVisibilityChanged:function(){return d},onNavigationIntent:function(){return f},pingVisibleLinks:function(){return h},unmountLinkInstance:function(){return c}}),r(38202);let n=r(89752),l=r(59154),a=r(50593),o="function"==typeof WeakMap?new WeakMap:new Map,u=new Set,i="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;d(t.target,e)}},{rootMargin:"200px"}):null;function s(e,t,r,l){let a=null;try{if(a=(0,n.createPrefetchURL)(t),null===a)return}catch(e){("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+t+"' because it cannot be converted to a URL.");return}let u={prefetchHref:a.href,router:r,kind:l,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1};void 0!==o.get(e)&&c(e),o.set(e,u),null!==i&&i.observe(e)}function c(e){let t=o.get(e);if(void 0!==t){o.delete(e),u.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==i&&i.unobserve(e)}function d(e,t){let r=o.get(e);void 0!==r&&(r.isVisible=t,t?u.add(r):u.delete(r),p(r))}function f(e){let t=o.get(e);void 0!==t&&void 0!==t&&(t.wasHoveredOrTouched=!0,p(t))}function p(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function h(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of u){let o=n.prefetchTask;if(null!==o&&n.cacheVersion===r&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let u=(0,a.createCacheKey)(n.prefetchHref,e),i=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(u,t,n.kind===l.PrefetchKind.FULL,i),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let n=r(5144),l=r(5334),a=new n.PromiseQueue(5),o=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[r,l]of Object.entries(e))if(Array.isArray(l))for(let e of l)t.append(r,n(e));else t.set(r,n(l));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return l}})},77022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let n=r(43210),l=r(51215),a="next-route-announcer";function o(e){let{tree:t}=e,[r,o]=(0,n.useState)(null);(0,n.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[u,i]=(0,n.useState)(""),s=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&i(e),s.current=e},[t]),r?(0,l.createPortal)(u,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let n=r(59008),l=r(57391),a=r(86770),o=r(2030),u=r(25232),i=r(59435),s=r(41500),c=r(89752),d=r(96493),f=r(68214),p=r(22308);function h(e,t){let{origin:r}=t,h={},g=e.canonicalUrl,y=e.tree;h.preserveCustomHistoryState=!1;let m=(0,c.createEmptyCacheNode)(),b=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);return m.lazyData=(0,n.fetchServerResponse)(new URL(g,r),{flightRouterState:[y[0],y[1],y[2],"refetch"],nextUrl:b?e.nextUrl:null}),m.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,u.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);for(let r of(m.lazyData=null,n)){let{tree:n,seedData:i,head:f,isRootRender:v}=r;if(!v)return console.log("REFRESH FAILED"),e;let _=(0,a.applyRouterStatePatchToTree)([""],y,n,e.canonicalUrl);if(null===_)return(0,d.handleSegmentMismatch)(e,t,n);if((0,o.isNavigatingToNewRootLayout)(y,_))return(0,u.handleExternalUrl)(e,h,g,e.pushRef.pendingPush);let P=c?(0,l.createHrefFromUrl)(c):void 0;if(c&&(h.canonicalUrl=P),null!==i){let e=i[1],t=i[3];m.rsc=e,m.prefetchRsc=null,m.loading=t,(0,s.fillLazyItemsTillLeafWithHead)(m,void 0,n,i,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({state:e,updatedTree:_,updatedCache:m,includeNextUrl:b,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=m,h.patchedTree=_,y=_}return(0,i.handleMutable)(e,h)},()=>e)}r(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return m},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return o},getURL:function(){return u},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,l=Array(n),a=0;a<n;a++)l[a]=arguments[a];return r||(r=!0,t=e(...l)),t}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>l.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=o();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},79551:e=>{"use strict";e.exports=require("url")},82704:()=>{},84545:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducer:function(){return u},useUnwrapState:function(){return o}});let n=r(40740)._(r(43210)),l=r(91992),a=r(61520);function o(e){return(0,l.isThenable)(e)?(0,n.use)(e):e}function u(e){let[t,r]=n.default.useState(e.state),l=(0,a.useSyncDevRenderIndicator)();return[t,(0,n.useCallback)(t=>{l(()=>{e.dispatch(t,r)})},[e,l])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},85814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return h}});let n=r(14985),l=r(60687),a=n._(r(43210)),o=r(30195),u=r(22142),i=r(59154),s=r(53038),c=r(79289),d=r(96127);r(50148);let f=r(73406);function p(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}let h=a.default.forwardRef(function(e,t){let r,n;let{href:o,as:h,children:g,prefetch:y=null,passHref:m,replace:b,shallow:v,scroll:_,onClick:P,onMouseEnter:x,onTouchStart:R,legacyBehavior:j=!1,...w}=e;r=g,j&&("string"==typeof r||"number"==typeof r)&&(r=(0,l.jsx)("a",{children:r}));let O=a.default.useContext(u.AppRouterContext),E=!1!==y,T=null===y?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:M,as:S}=a.default.useMemo(()=>{let e=p(o);return{href:e,as:h?p(h):e}},[o,h]);j&&(n=a.default.Children.only(r));let N=j?n&&"object"==typeof n&&n.ref:t,C=a.default.useCallback(e=>(E&&null!==O&&(0,f.mountLinkInstance)(e,M,O,T),()=>{(0,f.unmountLinkInstance)(e)}),[E,M,O,T]),A={ref:(0,s.useMergedRef)(C,N),onClick(e){j||"function"!=typeof P||P(e),j&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),O&&!e.defaultPrevented&&!function(e,t,r,n,l,o,u){let{nodeName:i}=e.currentTarget;!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e))&&(e.preventDefault(),a.default.startTransition(()=>{let e=null==u||u;"beforePopState"in t?t[l?"replace":"push"](r,n,{shallow:o,scroll:e}):t[l?"replace":"push"](n||r,{scroll:e})}))}(e,O,M,S,b,v,_)},onMouseEnter(e){j||"function"!=typeof x||x(e),j&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),O&&E&&(0,f.onNavigationIntent)(e.currentTarget)},onTouchStart:function(e){j||"function"!=typeof R||R(e),j&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),O&&E&&(0,f.onNavigationIntent)(e.currentTarget)}};return(0,c.isAbsoluteUrl)(S)?A.href=S:j&&!m&&("a"!==n.type||"href"in n.props)||(A.href=(0,d.addBasePath)(S)),j?a.default.cloneElement(n,A):(0,l.jsx)("a",{...w,...A,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,i){let s;let[c,d,f,p,h]=r;if(1===t.length){let e=u(r,n);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,i),e}let[g,y]=t;if(!(0,a.matchSegment)(g,c))return null;if(2===t.length)s=u(d[y],n);else if(null===(s=e((0,l.getNextFlightSegmentPath)(t),d[y],n,i)))return null;let m=[t[0],{...d,[y]:s},f,p];return h&&(m[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(m,i),m}}});let n=r(83913),l=r(74007),a=r(14077),o=r(22308);function u(e,t){let[r,l]=e,[o,i]=t;if(o===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(r,o)){let t={};for(let e in l)void 0!==i[e]?t[e]=u(l[e],i[e]):t[e]=l[e];for(let e in i)!t[e]&&(t[e]=i[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87498:(e,t,r)=>{Promise.resolve().then(r.bind(r,90597))},89752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return T},default:function(){return U}});let n=r(40740),l=r(60687),a=n._(r(43210)),o=r(22142),u=r(59154),i=r(57391),s=r(10449),c=r(84545),d=n._(r(35656)),f=r(35416),p=r(96127),h=r(77022),g=r(67086),y=r(44397),m=r(89330),b=r(25942),v=r(26736),_=r(70642),P=r(12776),x=r(11264);r(50593);let R=r(36875),j=r(97860),w=r(75076);r(73406);let O={};function E(e){return e.origin!==window.location.origin}function T(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return E(t)?null:t}function M(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,l={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,i.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(l,"",n)):window.history.replaceState(l,"",n)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null}}function N(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,l=null!==n?n:r;return(0,a.useDeferredValue)(r,l)}function A(e){let t,{actionQueue:r,assetPrefix:n,globalError:i}=e,[f,P]=(0,c.useReducer)(r),{canonicalUrl:S}=(0,c.useUnwrapState)(f),{searchParams:A,pathname:U}=(0,a.useMemo)(()=>{let e=new URL(S,"http://n");return{searchParams:e.searchParams,pathname:(0,v.hasBasePath)(e.pathname)?(0,b.removeBasePath)(e.pathname):e.pathname}},[S]),k=(0,a.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,a.startTransition)(()=>{P({type:u.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[P]),L=(0,a.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return P({type:u.ACTION_NAVIGATE,url:n,isExternalUrl:E(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t,allowAliasing:!0})},[P]);(0,x.useServerActionDispatcher)(P);let I=(0,a.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=T(e);if(null!==n){var l;(0,w.prefetchReducer)(r.state,{type:u.ACTION_PREFETCH,url:n,kind:null!=(l=null==t?void 0:t.kind)?l:u.PrefetchKind.FULL})}},replace:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;L(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,a.startTransition)(()=>{var r;L(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,a.startTransition)(()=>{P({type:u.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}}),[r,P,L]);(0,a.useEffect)(()=>{window.next&&(window.next.router=I)},[I]),(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(O.pendingMpaPath=void 0,P({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[P]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,j.isRedirectError)(t)){e.preventDefault();let r=(0,R.getURLFromRedirectError)(t);(0,R.getRedirectTypeFromError)(t)===j.RedirectType.push?I.push(r,{}):I.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[I]);let{pushRef:H}=(0,c.useUnwrapState)(f);if(H.mpaNavigation){if(O.pendingMpaPath!==S){let e=window.location;H.pendingPush?e.assign(S):e.replace(S),O.pendingMpaPath=S}(0,a.use)(m.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{P({type:u.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=N(t),l&&r(l)),e(t,n,l)},window.history.replaceState=function(e,n,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=N(e),l&&r(l)),t(e,n,l)};let n=e=>{if(e.state){if(!e.state.__NA){window.location.reload();return}(0,a.startTransition)(()=>{P({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:e.state.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[P]);let{cache:F,tree:z,nextUrl:K,focusAndScrollRef:B}=(0,c.useUnwrapState)(f),G=(0,a.useMemo)(()=>(0,y.findHeadInCache)(F,z[1]),[F,z]),q=(0,a.useMemo)(()=>(0,_.getSelectedParams)(z),[z]),V=(0,a.useMemo)(()=>({parentTree:z,parentCacheNode:F,parentSegmentPath:null,url:S}),[z,F,S]),W=(0,a.useMemo)(()=>({changeByServerResponse:k,tree:z,focusAndScrollRef:B,nextUrl:K}),[k,z,B,K]);if(null!==G){let[e,r]=G;t=(0,l.jsx)(C,{headCacheNode:e},r)}else t=null;let Y=(0,l.jsxs)(g.RedirectBoundary,{children:[t,F.rsc,(0,l.jsx)(h.AppRouterAnnouncer,{tree:z})]});return Y=(0,l.jsx)(d.ErrorBoundary,{errorComponent:i[0],errorStyles:i[1],children:Y}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(M,{appRouterState:(0,c.useUnwrapState)(f)}),(0,l.jsx)(D,{}),(0,l.jsx)(s.PathParamsContext.Provider,{value:q,children:(0,l.jsx)(s.PathnameContext.Provider,{value:U,children:(0,l.jsx)(s.SearchParamsContext.Provider,{value:A,children:(0,l.jsx)(o.GlobalLayoutRouterContext.Provider,{value:W,children:(0,l.jsx)(o.AppRouterContext.Provider,{value:I,children:(0,l.jsx)(o.LayoutRouterContext.Provider,{value:V,children:Y})})})})})})]})}function U(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:a}=e;return(0,P.useNavFailureHandler)(),(0,l.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,l.jsx)(A,{actionQueue:t,assetPrefix:a,globalError:[r,n]})})}let k=new Set,L=new Set;function D(){let[,e]=a.default.useState(0),t=k.size;return(0,a.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==k.size&&r(),()=>{L.delete(r)}},[t,e]),[...k].map((e,t)=>(0,l.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90597:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\legal-case-dashboard\\\\my-app\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\page.tsx","default")},91992:(e,t)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return r}})},93641:()=>{},94635:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview/i},96127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(98834),l=r(54674);function a(e,t){return(0,l.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(49384),l=r(82348);function a(...e){return(0,l.QP)((0,n.$)(e))}},96493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let n=r(25232);function l(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let o=a.length<=2,[u,i]=a,s=(0,l.createRouterCacheKey)(i),c=r.parallelRoutes.get(u),d=t.parallelRoutes.get(u);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(u,d));let f=null==c?void 0:c.get(s),p=d.get(s);if(o){p&&p.lazyData&&p!==f||d.set(s,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}if(!p||!f){p||d.set(s,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(s,p)),e(p,f,(0,n.getNextFlightSegmentPath)(a))}}});let n=r(74007),l=r(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(59008),r(57391),r(86770),r(2030),r(25232),r(59435),r(56928),r(89752),r(96493),r(68214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return l}});let n=r(19169);function l(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:l,hash:a}=(0,n.parsePath)(e);return""+t+r+l+a}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,423,86],()=>r(42626));module.exports=n})();