(()=>{var e={};e.id=937,e.ids=[937],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9222:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\legal-case-dashboard\\\\my-app\\\\app\\\\trimbakeshwar\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\trimbakeshwar\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38230:(e,s,a)=>{Promise.resolve().then(a.bind(a,9222))},49854:(e,s,a)=>{Promise.resolve().then(a.bind(a,77682))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65140:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),l=a(48088),r=a(88170),n=a.n(r),i=a(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let c={children:["",{children:["trimbakeshwar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9222)),"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\trimbakeshwar\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\trimbakeshwar\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/trimbakeshwar/page",pathname:"/trimbakeshwar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},77682:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(60687),l=a(43210),r=a(14719),n=a(78005),i=a(40228),d=a(48730),c=a(27900),o=a(78122),m=a(28559),x=a(30382),h=a(97992),p=a(99270),u=a(80462),g=a(31158),b=a(15879),j=a(53186),N=a(24934),v=a(68988),f=a(55192),w=a(96752),C=a(63974),y=a(59821),k=a(80189),A=a(55629),S=a(37826),T=a(15616),D=a(3855);function P(){let{cases:e,loading:s,error:a,lastUpdated:P,updateCasesFromCsv:$,refreshCases:L,addCase:_}=(0,D.L)(),[E,R]=(0,l.useState)(""),[q,M]=(0,l.useState)("All Types"),[O,U]=(0,l.useState)("All Statuses"),[z,F]=(0,l.useState)(1),[G,I]=(0,l.useState)(null),[V,B]=(0,l.useState)("asc"),[W,Z]=(0,l.useState)(10),[H,X]=(0,l.useState)({}),[K,Q]=(0,l.useState)({}),[Y,J]=(0,l.useState)({}),[ee,es]=(0,l.useState)({}),[ea,et]=(0,l.useState)(""),[el,er]=(0,l.useState)(null),[en,ei]=(0,l.useState)(!1),[ed,ec]=(0,l.useState)({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),eo=(0,l.useMemo)(()=>e.filter(e=>"Trimbakeshwar"===e.taluka),[e]),em=(0,l.useMemo)(()=>["All Types",...new Set(eo.map(e=>e.caseType).filter(Boolean))],[eo]),ex=(0,l.useMemo)(()=>["All Statuses",...new Set(eo.map(e=>K[e.caseNumber]||e.received).filter(Boolean))],[eo,K]),eh=new Date,ep=(0,l.useMemo)(()=>{let e=eo.filter(e=>{let s=e.caseNumber?.toLowerCase().includes(E.toLowerCase())||e.appellant?.toLowerCase().includes(E.toLowerCase())||e.respondent?.toLowerCase().includes(E.toLowerCase())||e.caseType?.toLowerCase().includes(E.toLowerCase()),a="All Types"===q||e.caseType===q,t="All Statuses"===O||(K[e.caseNumber]||e.received)===O;return s&&a&&t});return G&&e.sort((e,s)=>{let a=e[G],t=s[G];return("date"===G&&(a=new Date(a).getTime(),t=new Date(t).getTime()),a<t)?"asc"===V?-1:1:a>t?"asc"===V?1:-1:0}),e},[eo,E,q,O,G,V]),eu=Math.ceil(ep.length/W),eg=(z-1)*W,eb=ep.slice(eg,eg+W),ej=(0,l.useMemo)(()=>{let e=ep.length;return{total:e,received:ep.filter(e=>"प्राप्त"===(K[e.caseNumber]||e.received)).length,nextDate:ep.filter(e=>""!==(Y[e.caseNumber]||e.nextDate||"").trim()).length}},[ep,K,Y]),eN=e=>{G===e?B("asc"===V?"desc":"asc"):(I(e),B("asc")),F(1)},ev=e=>{F(e)},ef=(e,s)=>{X(a=>({...a,[e]:s}))},ew=(e,s)=>{Q(a=>({...a,[e]:s}))},eC=(e,s)=>{J(a=>({...a,[e]:s}))},ey=(e,s)=>{er(e),et(H[e]||s||""),es(s=>({...s,[e]:!0}))},ek=e=>{es(s=>({...s,[e]:!1})),er(null),et("")},eA=e=>{ef(e,ea),ek(e)},eS=e=>{let s=ep.map(e=>({Date:e.date,"Case Type":e.caseType,"Case Number":e.caseNumber,Year:e.year,Appellant:e.appellant,Respondent:e.respondent,Received:K[e.caseNumber]||e.received||"-",Status:H[e.caseNumber]||e.status||""}));if("CSV"===e){let e=new Blob([[Object.keys(s[0]).join(","),...s.map(e=>Object.values(e).join(","))].join("\n")],{type:"text/csv"}),a=window.URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download=`trimbakeshwar-cases-${new Date().toISOString().split("T")[0]}.csv`,t.click(),window.URL.revokeObjectURL(a)}else alert(`${e} export functionality would be implemented here`)},eT=e=>{let s=e?.toLowerCase()||"";if(s.includes("completed"))return{variant:"default",icon:r.A,color:"text-green-700"};if(s.includes("received"))return{variant:"secondary",icon:n.A,color:"text-indigo-700"};if(s.includes("scheduled"))return{variant:"outline",icon:i.A,color:"text-blue-700"};if(s.includes("review"))return{variant:"secondary",icon:d.A,color:"text-cyan-700"};if(s.includes("issued"))return{variant:"outline",icon:c.A,color:"text-purple-700"};else return{variant:"outline",icon:d.A,color:"text-gray-700"}};return s?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50/20 to-blue-50/20 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-indigo-600"}),(0,t.jsx)("p",{className:"text-indigo-700",children:"Loading cases..."})]})}):(0,t.jsxs)(k.Bc,{children:[(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50/20 to-blue-50/20",children:(0,t.jsxs)("div",{className:"mx-auto max-w-7xl space-y-4 p-3 sm:p-4",children:[(0,t.jsxs)("div",{className:"text-center space-y-2 pb-4 border-b bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm border-indigo-100",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,t.jsxs)(N.$,{variant:"ghost",size:"sm",onClick:()=>window.location.href="/",className:"flex items-center gap-2 text-indigo-700 hover:text-indigo-900 hover:bg-indigo-100 self-start sm:self-center",children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Back to Home"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,t.jsxs)("div",{className:"text-center sm:text-left",children:[(0,t.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-indigo-900",children:"Trimbakeshwar Legal Case Dashboard"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-indigo-700",children:"Sub-Divisional Magistrate Office, Nashik"})]})]}),(0,t.jsx)("div",{className:"hidden sm:block w-24"})," "]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-6 text-xs text-indigo-600",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(h.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:"Trimbakeshwar Subdivision"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(i.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:eh.toLocaleDateString("en-IN",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4",children:[(0,t.jsx)(f.Zp,{className:"border border-indigo-100 shadow-sm bg-gradient-to-br from-indigo-50/50 to-white",children:(0,t.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-indigo-600 mb-1",children:ej.total}),(0,t.jsx)("div",{className:"text-xs text-indigo-700",children:"Total Cases"})]})}),(0,t.jsx)(f.Zp,{className:"border border-green-100 shadow-sm bg-gradient-to-br from-green-50/50 to-white",children:(0,t.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600 mb-1",children:ej.received}),(0,t.jsx)("div",{className:"text-xs text-green-700",children:"Received"})]})}),(0,t.jsx)(f.Zp,{className:"border border-blue-100 shadow-sm bg-gradient-to-br from-blue-50/50 to-white",children:(0,t.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-blue-600 mb-1",children:ej.nextDate}),(0,t.jsx)("div",{className:"text-xs text-blue-700",children:"Next Date"})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(f.Zp,{className:"border border-indigo-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)(f.Wu,{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400 h-4 w-4"}),(0,t.jsx)(v.p,{placeholder:"Search cases...",value:E,onChange:e=>R(e.target.value),className:"pl-10 h-10 border-indigo-200 focus:border-indigo-500 bg-white/50"})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-wrap",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,t.jsxs)(C.l6,{value:q,onValueChange:M,children:[(0,t.jsx)(C.bq,{className:"h-9 text-sm border-indigo-200 bg-white/50 min-w-[120px]",children:(0,t.jsx)(C.yv,{placeholder:"Type"})}),(0,t.jsx)(C.gC,{children:em.map(e=>(0,t.jsx)(C.eb,{value:e,children:e},e))})]}),(0,t.jsxs)(C.l6,{value:O,onValueChange:U,children:[(0,t.jsx)(C.bq,{className:"h-9 text-sm border-indigo-200 bg-white/50 min-w-[120px]",children:(0,t.jsx)(C.yv,{placeholder:"Status"})}),(0,t.jsx)(C.gC,{children:ex.map(e=>(0,t.jsx)(C.eb,{value:e,children:e},e))})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,t.jsxs)(N.$,{onClick:()=>ei(!0),className:"h-9 text-sm bg-indigo-600 hover:bg-indigo-700 text-white",children:[(0,t.jsx)("span",{className:"text-lg mr-1",children:"+"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Add New Case"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Add"})]}),(0,t.jsxs)(N.$,{variant:"outline",onClick:()=>{R(""),M("All Types"),U("All Statuses"),F(1),I(null)},className:"h-9 text-sm bg-white/50 border-indigo-200 hover:bg-indigo-50",children:[(0,t.jsx)(u.A,{className:"h-3 w-3 mr-1"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Clear"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Clear"})]}),(0,t.jsxs)(A.rI,{children:[(0,t.jsx)(A.ty,{asChild:!0,children:(0,t.jsxs)(N.$,{variant:"outline",className:"h-9 text-sm bg-white/50 border-indigo-200 hover:bg-indigo-50",children:[(0,t.jsx)(g.A,{className:"h-3 w-3 mr-1"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Export"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Export"})]})}),(0,t.jsxs)(A.SQ,{children:[(0,t.jsx)(A._2,{onClick:()=>eS("CSV"),children:"Export as CSV"}),(0,t.jsx)(A._2,{onClick:()=>eS("PDF"),children:"Export as PDF"}),(0,t.jsx)(A._2,{onClick:()=>eS("Excel"),children:"Export as Excel"})]})]}),(0,t.jsxs)(N.$,{variant:"outline",onClick:L,disabled:s,className:"h-9 text-sm bg-white/50 border-indigo-200 hover:bg-indigo-50",children:[(0,t.jsx)(o.A,{className:`h-3 w-3 mr-1 ${s?"animate-spin":""}`}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Refresh"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Refresh"})]})]})]}),(0,t.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 text-sm text-indigo-700",children:(0,t.jsxs)("span",{children:["Showing ",eb.length," of ",ep.length," cases"]})})]})}),(0,t.jsx)(f.Zp,{className:"border border-indigo-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,t.jsxs)(f.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"overflow-x-auto min-w-full",children:(0,t.jsxs)(w.XI,{className:"min-w-[800px]",children:[(0,t.jsx)(w.A0,{children:(0,t.jsxs)(w.Hj,{className:"border-b border-indigo-100",children:[(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[100px]",children:"Case Type"}),(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[140px]",children:(0,t.jsxs)(N.$,{variant:"ghost",onClick:()=>eN("caseNumber"),className:"h-auto p-0 font-semibold hover:bg-indigo-50",children:["Case Number","caseNumber"===G&&("asc"===V?(0,t.jsx)(b.A,{className:"ml-1 h-3 w-3"}):(0,t.jsx)(j.A,{className:"ml-1 h-3 w-3"}))]})}),(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[120px]",children:"Appellant"}),(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[120px]",children:"Respondent"}),(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[80px]",children:"Received"}),(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[140px]",children:"Next Date"}),(0,t.jsx)(w.nd,{className:"font-semibold text-indigo-900 min-w-[120px]",children:"Status"})]})}),(0,t.jsx)(w.BF,{children:eb.map((e,s)=>(eT(e.status).icon,(0,t.jsxs)(w.Hj,{className:"hover:bg-indigo-50/50",children:[(0,t.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,t.jsx)(y.E,{variant:"outline",className:"text-xs border-indigo-200 text-indigo-700 whitespace-nowrap",children:e.caseType})}),(0,t.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,t.jsx)("div",{className:"font-semibold text-sm break-all",children:e.caseNumber})}),(0,t.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,t.jsx)("div",{className:"font-medium text-sm break-words",children:e.appellant})}),(0,t.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,t.jsx)("div",{className:"text-sm break-words",children:e.respondent})}),(0,t.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,t.jsxs)(C.l6,{value:K[e.caseNumber]||e.received||"-",onValueChange:s=>ew(e.caseNumber,s),children:[(0,t.jsx)(C.bq,{className:"w-full min-w-[70px] h-8 text-xs border-indigo-200",children:(0,t.jsx)(C.yv,{})}),(0,t.jsxs)(C.gC,{children:[(0,t.jsx)(C.eb,{value:"प्राप्त",children:"प्राप्त"}),(0,t.jsx)(C.eb,{value:"-",children:"-"})]})]})}),(0,t.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,t.jsx)(v.p,{type:"date",value:Y[e.caseNumber]||e.nextDate||"2025-07-17",onChange:s=>eC(e.caseNumber,s.target.value),className:"w-full min-w-[130px] h-8 text-xs border-indigo-200 focus:border-indigo-400"})}),(0,t.jsx)(w.nA,{children:(0,t.jsxs)(S.lG,{open:ee[e.caseNumber]||!1,onOpenChange:s=>{s||ek(e.caseNumber)},children:[(0,t.jsx)(S.zM,{asChild:!0,children:(0,t.jsxs)(N.$,{variant:"outline",size:"sm",className:"text-xs h-8 min-w-[120px] justify-start",onClick:()=>ey(e.caseNumber,e.status),children:[(H[e.caseNumber]||e.status||"Enter status...").substring(0,15),(H[e.caseNumber]||e.status||"").length>15?"...":""]})}),(0,t.jsxs)(S.Cf,{className:"sm:max-w-[425px]",children:[(0,t.jsx)(S.c7,{children:(0,t.jsxs)(S.L3,{children:["Edit Status - Case #",e.caseNumber]})}),(0,t.jsx)("div",{className:"grid gap-4 py-4",children:(0,t.jsx)(T.T,{value:ea,onChange:e=>et(e.target.value),placeholder:"Enter detailed status information...",className:"min-h-[100px]"})}),(0,t.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,t.jsx)(N.$,{variant:"outline",onClick:()=>ek(e.caseNumber),children:"Cancel"}),(0,t.jsx)(N.$,{onClick:()=>eA(e.caseNumber),children:"Save"})]})]})]})})]},`${e.caseNumber}-${s}`)))})]})}),eu>1&&(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-3 p-4 border-t border-indigo-100",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{className:"text-sm text-indigo-700",children:["Page ",z," of ",eu]}),(0,t.jsxs)(C.l6,{value:W.toString(),onValueChange:e=>{Z(Number(e)),F(1)},children:[(0,t.jsx)(C.bq,{className:"w-20 h-8 border-indigo-200",children:(0,t.jsx)(C.yv,{})}),(0,t.jsxs)(C.gC,{children:[(0,t.jsx)(C.eb,{value:"5",children:"5"}),(0,t.jsx)(C.eb,{value:"10",children:"10"}),(0,t.jsx)(C.eb,{value:"20",children:"20"}),(0,t.jsx)(C.eb,{value:"50",children:"50"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-1 sm:gap-2",children:[(0,t.jsxs)(N.$,{variant:"outline",size:"sm",onClick:()=>ev(z-1),disabled:1===z,className:"border-indigo-200 hover:bg-indigo-50 text-xs sm:text-sm px-2 sm:px-3",children:[(0,t.jsx)("span",{className:"hidden sm:inline",children:"Previous"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Prev"})]}),Array.from({length:Math.min(5,eu)},(e,s)=>{let a=Math.max(1,Math.min(eu-4,z-2))+s;return(0,t.jsx)(N.$,{variant:a===z?"default":"outline",size:"sm",onClick:()=>ev(a),className:`w-7 h-7 sm:w-8 sm:h-8 p-0 text-xs sm:text-sm ${a===z?"bg-indigo-600 hover:bg-indigo-700":"border-indigo-200 hover:bg-indigo-50"}`,children:a},a)}),(0,t.jsx)(N.$,{variant:"outline",size:"sm",onClick:()=>ev(z+1),disabled:z===eu,className:"border-indigo-200 hover:bg-indigo-50 text-xs sm:text-sm px-2 sm:px-3",children:"Next"})]})]})]})})]})]})}),(0,t.jsx)(S.lG,{open:en,onOpenChange:ei,children:(0,t.jsxs)(S.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsx)(S.c7,{children:(0,t.jsx)(S.L3,{children:"Add New Case - Trimbakeshwar"})}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)("label",{htmlFor:"caseType",className:"text-right text-sm font-medium",children:"Case Type *"}),(0,t.jsxs)(C.l6,{value:ed.caseType,onValueChange:e=>ec(s=>({...s,caseType:e})),children:[(0,t.jsx)(C.bq,{className:"col-span-3",children:(0,t.jsx)(C.yv,{})}),(0,t.jsxs)(C.gC,{children:[(0,t.jsx)(C.eb,{value:"अपील",children:"अपील"}),(0,t.jsx)(C.eb,{value:"रिव्हीजन",children:"रिव्हीजन"}),(0,t.jsx)(C.eb,{value:"मामलेदार कोर्ट",children:"मामलेदार कोर्ट"}),(0,t.jsx)(C.eb,{value:"गौणखनिज",children:"गौणखनिज"}),(0,t.jsx)(C.eb,{value:"अतिक्रमण",children:"अतिक्रमण"}),(0,t.jsx)(C.eb,{value:"कुळ कायदा",children:"कुळ कायदा"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)("label",{htmlFor:"caseNumber",className:"text-right text-sm font-medium",children:"Case Number *"}),(0,t.jsx)(v.p,{id:"caseNumber",value:ed.caseNumber,onChange:e=>ec(s=>({...s,caseNumber:e.target.value})),className:"col-span-3",placeholder:"e.g., अपील/150/2023"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)("label",{htmlFor:"appellant",className:"text-right text-sm font-medium",children:"Appellant *"}),(0,t.jsx)(v.p,{id:"appellant",value:ed.appellant,onChange:e=>ec(s=>({...s,appellant:e.target.value})),className:"col-span-3",placeholder:"Appellant name"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)("label",{htmlFor:"respondent",className:"text-right text-sm font-medium",children:"Respondent *"}),(0,t.jsx)(v.p,{id:"respondent",value:ed.respondent,onChange:e=>ec(s=>({...s,respondent:e.target.value})),className:"col-span-3",placeholder:"Respondent name"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,t.jsx)(N.$,{variant:"outline",onClick:()=>ei(!1),children:"Cancel"}),(0,t.jsx)(N.$,{onClick:()=>{if(!ed.caseNumber||!ed.appellant||!ed.respondent){alert("Please fill in all required fields");return}let e=_({date:new Date().toISOString().split("T")[0],caseType:ed.caseType,caseNumber:ed.caseNumber,appellant:ed.appellant,respondent:ed.respondent,received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Trimbakeshwar",filedDate:new Date().toISOString().split("T")[0],lastUpdate:new Date().toISOString().split("T")[0]});e.success?(ec({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),ei(!1),alert("Case added successfully!")):alert(`Failed to add case: ${e.error}`)},className:"bg-indigo-600 hover:bg-indigo-700",children:"Add Case"})]})]})})]})}function $(){return(0,t.jsx)(P,{})}},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,423,86,657,807],()=>a(65140));module.exports=t})();