globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/trimbakeshwar/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"894":{"*":{"id":"86346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"97173","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"28827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"27924","name":"*","chunks":[],"async":false}},"5127":{"*":{"id":"77682","name":"*","chunks":[],"async":false}},"5459":{"*":{"id":"45987","name":"*","chunks":[],"async":false}},"5794":{"*":{"id":"51119","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"35656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"40099","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"38243","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"62763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2353,"name":"*","chunks":["177","static/chunks/app/layout-9a118d2042dccac3.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7275,"name":"*","chunks":["177","static/chunks/app/layout-9a118d2042dccac3.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\globals.css":{"id":9324,"name":"*","chunks":["177","static/chunks/app/layout-9a118d2042dccac3.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\igatpuri\\page.tsx":{"id":5794,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\page.tsx":{"id":5459,"name":"*","chunks":["863","static/chunks/863-7bb27b6f55d8a0c2.js","974","static/chunks/app/page-c6e0830eca7da453.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\trimbakeshwar\\page.tsx":{"id":5127,"name":"*","chunks":["863","static/chunks/863-7bb27b6f55d8a0c2.js","822","static/chunks/822-9fe2bab21cd4dfa3.js","523","static/chunks/523-ff72399b298c051c.js","937","static/chunks/app/trimbakeshwar/page-9dc41bcfe1121aef.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\":[],"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\layout":[{"inlined":false,"path":"static/css/77a8d527448b7a78.css"}],"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\page":[],"C:\\Users\\<USER>\\Downloads\\legal-case-dashboard\\my-app\\app\\trimbakeshwar\\page":[]},"rscModuleMapping":{"894":{"*":{"id":"16444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"31307","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"12089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"16042","name":"*","chunks":[],"async":false}},"5127":{"*":{"id":"9222","name":"*","chunks":[],"async":false}},"5459":{"*":{"id":"90597","name":"*","chunks":[],"async":false}},"5794":{"*":{"id":"81703","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"88170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"49477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"29345","name":"*","chunks":[],"async":false}},"9324":{"*":{"id":"82704","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"46577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}