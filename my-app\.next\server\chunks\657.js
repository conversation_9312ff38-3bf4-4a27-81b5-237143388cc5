"use strict";exports.id=657,exports.ids=[657],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(43210);n(60687);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9510:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(43210),o=n(11273),i=n(98599),a=n(8730),l=n(60687);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.TL)(f),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(f,n),a=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(p,{ref:a,children:r})});m.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,a.TL)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,u=r.useRef(null),s=(0,i.s)(t,u),d=c(h,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...a}),()=>void d.itemMap.delete(u))),(0,l.jsx)(g,{[v]:"",ref:s,children:o})});return y.displayName=h,[{Provider:d,Slot:m,ItemSlot:y},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}var s=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=d(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},9989:(e,t,n)=>{n.d(t,{Kq:()=>I});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(31355),u=(n(96963),n(38674)),s=(n(25028),n(46059)),c=n(14163),d=n(8730),f=(n(65551),n(69024)),p=n(60687),[m,h]=(0,a.A)("Tooltip",[u.Bk]),v=(0,u.Bk)(),g="TooltipProvider",y="tooltip.open",[w,x]=m(g),b=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,l=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,p.jsx)(w,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),l.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:a})};b.displayName=g;var E="Tooltip",[C,R]=m(E),S="TooltipTrigger";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=R(S,n),s=x(S,n),d=v(n),f=r.useRef(null),m=(0,i.s)(t,f,l.onTriggerChange),h=r.useRef(!1),g=r.useRef(!1),y=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,p.jsx)(u.Mz,{asChild:!0,...d,children:(0,p.jsx)(c.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:m,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||g.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),g.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),g.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),h.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})}).displayName=S;var[M,k]=m("TooltipPortal",{forceMount:void 0}),T="TooltipContent",A=r.forwardRef((e,t)=>{let n=k(T,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,a=R(T,e.__scopeTooltip);return(0,p.jsx)(s.C,{present:r||a.open,children:a.disableHoverableContent?(0,p.jsx)(L,{side:o,...i,ref:t}):(0,p.jsx)(j,{side:o,...i,ref:t})})}),j=r.forwardRef((e,t)=>{let n=R(T,e.__scopeTooltip),o=x(T,e.__scopeTooltip),a=r.useRef(null),l=(0,i.s)(t,a),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,f=a.current,{onPointerInTransitChange:m}=o,h=r.useCallback(()=>{s(null),m(!1)},[m]),v=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),m(!0)},[m]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&f){let e=e=>v(e,f),t=e=>v(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,v,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=c?.contains(t)||f?.contains(t),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,s=a.y,c=l.x,d=l.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}(n,u);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,d,h]),(0,p.jsx)(L,{...e,ref:l})}),[D,N]=m(E,{isInside:!1}),P=(0,d.Dc)("TooltipContent"),L=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:s,...c}=e,d=R(T,n),m=v(n),{onClose:h}=d;return r.useEffect(()=>(document.addEventListener(y,h),()=>document.removeEventListener(y,h)),[h]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,p.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,p.jsxs)(u.UC,{"data-state":d.stateAttribute,...m,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,p.jsx)(P,{children:o}),(0,p.jsx)(D,{scope:n,isInside:!0,children:(0,p.jsx)(f.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});A.displayName=T;var O="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=v(n);return N(O,n).isInside?null:(0,p.jsx)(u.i3,{...o,...r,ref:t})}).displayName=O;var I=b},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(43210),o=n(60687);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[l]||a,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>l});var r=n(43210),o=n(51215),i=n(8730),a=n(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14719:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},15879:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]])},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),i=n(14163),a=n(66156),l=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[s,c]=r.useState(!1);(0,a.N)(()=>c(!0),[]);let d=n||s&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},26134:(e,t,n)=>{n.d(t,{UC:()=>et,ZL:()=>J,bL:()=>Z,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>Q});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(96963),u=n(65551),s=n(31355),c=n(32547),d=n(25028),f=n(46059),p=n(14163),m=n(1359),h=n(42247),v=n(63376),g=n(8730),y=n(60687),w="Dialog",[x,b]=(0,a.A)(w),[E,C]=x(w),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:i??!1,onChange:a,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};R.displayName=w;var S="DialogTrigger",M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(S,n),l=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":V(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});M.displayName=S;var k="DialogPortal",[T,A]=x(k,{forceMount:void 0}),j=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=C(k,t);return(0,y.jsx)(T,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};j.displayName=k;var D="DialogOverlay",N=r.forwardRef((e,t)=>{let n=A(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=C(D,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(L,{...o,ref:t})}):null});N.displayName=D;var P=(0,g.TL)("DialogOverlay.RemoveScroll"),L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(D,n);return(0,y.jsx)(h.A,{as:P,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":V(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),O="DialogContent",I=r.forwardRef((e,t)=>{let n=A(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=C(O,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(_,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});I.displayName=O;var _=r.forwardRef((e,t)=>{let n=C(O,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=C(O,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...u}=e,d=C(O,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:d.titleId}),(0,y.jsx)(Y,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(W,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});H.displayName=W;var G="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(G,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=G;var K="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=C(K,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=K;var z="DialogTitleWarning",[$,q]=(0,a.q)(z,{contentName:O,titleName:W,docsSlug:"dialog"}),X=({titleId:e})=>{let t=q(z),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},Y=({contentRef:e,descriptionId:t})=>{let n=q("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},Z=R,Q=M,J=j,ee=N,et=I,en=H,er=U},27900:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},28559:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29398:(e,t,n)=>{n.d(t,{UC:()=>e9,q7:()=>e8,ZL:()=>e4,bL:()=>e5,l9:()=>e3});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(65551),u=n(14163),s=n(9510),c=n(43),d=n(31355),f=n(1359),p=n(32547),m=n(96963),h=n(38674),v=n(25028),g=n(46059),y=n(13495),w=n(60687),x="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[C,R,S]=(0,s.N)(E),[M,k]=(0,a.A)(E,[S]),[T,A]=M(E),j=r.forwardRef((e,t)=>(0,w.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(D,{...e,ref:t})})}));j.displayName=E;var D=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:s=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:v=!1,...g}=e,C=r.useRef(null),S=(0,i.s)(t,C),M=(0,c.jH)(d),[k,A]=(0,l.i)({prop:f,defaultProp:p??null,onChange:m,caller:E}),[j,D]=r.useState(!1),N=(0,y.c)(h),P=R(n),L=r.useRef(!1),[I,_]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(x,N),()=>e.removeEventListener(x,N)},[N]),(0,w.jsx)(T,{scope:n,orientation:a,dir:M,loop:s,currentTabStopId:k,onItemFocus:r.useCallback(e=>A(e),[A]),onItemShiftTab:r.useCallback(()=>D(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,w.jsx)(u.sG.div,{tabIndex:j||0===I?-1:0,"data-orientation":a,...g,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(x,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),v)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>D(!1))})})}),N="RovingFocusGroupItem",P=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:s,...c}=e,d=(0,m.B)(),f=l||d,p=A(N,n),h=p.currentTabStopId===f,v=R(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:x}=p;return r.useEffect(()=>{if(i)return g(),()=>y()},[i,g,y]),(0,w.jsx)(C.ItemSlot,{scope:n,id:f,focusable:i,active:a,children:(0,w.jsx)(u.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>O(n))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=x}):s})})});P.displayName=N;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=n(8730),_=n(63376),F=n(42247),B=["Enter"," "],W=["ArrowUp","PageDown","End"],H=["ArrowDown","PageUp","Home",...W],G={ltr:[...B,"ArrowRight"],rtl:[...B,"ArrowLeft"]},K={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[V,z,$]=(0,s.N)(U),[q,X]=(0,a.A)(U,[$,h.Bk,k]),Y=(0,h.Bk)(),Z=k(),[Q,J]=q(U),[ee,et]=q(U),en=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=Y(t),[s,d]=r.useState(null),f=r.useRef(!1),p=(0,y.c)(a),m=(0,c.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(h.bL,{...u,children:(0,w.jsx)(Q,{scope:t,open:n,onOpenChange:p,content:s,onContentChange:d,children:(0,w.jsx)(ee,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};en.displayName=U;var er=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=Y(n);return(0,w.jsx)(h.Mz,{...o,...r,ref:t})});er.displayName="MenuAnchor";var eo="MenuPortal",[ei,ea]=q(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=J(eo,t);return(0,w.jsx)(ei,{scope:t,forceMount:n,children:(0,w.jsx)(g.C,{present:n||i.open,children:(0,w.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};el.displayName=eo;var eu="MenuContent",[es,ec]=q(eu),ed=r.forwardRef((e,t)=>{let n=ea(eu,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=J(eu,e.__scopeMenu),a=et(eu,e.__scopeMenu);return(0,w.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:r||i.open,children:(0,w.jsx)(V.Slot,{scope:e.__scopeMenu,children:a.modal?(0,w.jsx)(ef,{...o,ref:t}):(0,w.jsx)(ep,{...o,ref:t})})})})}),ef=r.forwardRef((e,t)=>{let n=J(eu,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,_.Eq)(e)},[]),(0,w.jsx)(eh,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ep=r.forwardRef((e,t)=>{let n=J(eu,e.__scopeMenu);return(0,w.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),em=(0,I.TL)("MenuContent.ScrollLock"),eh=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:E,...C}=e,R=J(eu,n),S=et(eu,n),M=Y(n),k=Z(n),T=z(n),[A,D]=r.useState(null),N=r.useRef(null),P=(0,i.s)(t,N,R.onContentChange),L=r.useRef(0),O=r.useRef(""),I=r.useRef(0),_=r.useRef(null),B=r.useRef("right"),G=r.useRef(0),K=E?F.A:r.Fragment,U=e=>{let t=O.current+e,n=T().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;(function e(t){O.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))})(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(L.current),[]),(0,f.Oh)();let V=r.useCallback(e=>B.current===_.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,s=a.y,c=l.x,d=l.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,_.current?.area),[]);return(0,w.jsx)(es,{scope:n,searchRef:O,onItemEnter:r.useCallback(e=>{V(e)&&e.preventDefault()},[V]),onItemLeave:r.useCallback(e=>{V(e)||(N.current?.focus(),D(null))},[V]),onTriggerLeave:r.useCallback(e=>{V(e)&&e.preventDefault()},[V]),pointerGraceTimerRef:I,onPointerGraceIntentChange:r.useCallback(e=>{_.current=e},[]),children:(0,w.jsx)(K,{...E?{as:em,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),N.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,w.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,w.jsx)(j,{asChild:!0,...k,dir:S.dir,orientation:"vertical",loop:a,currentTabStopId:A,onCurrentTabStopIdChange:D,onEntryFocus:(0,o.m)(m,e=>{S.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eH(R.open),"data-radix-menu-content":"",dir:S.dir,...M,...C,ref:P,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&U(e.key));let o=N.current;if(e.target!==o||!H.includes(e.key))return;e.preventDefault();let i=T().filter(e=>!e.disabled).map(e=>e.ref.current);W.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eU(e=>{let t=e.target,n=G.current!==e.clientX;e.currentTarget.contains(t)&&n&&(B.current=e.clientX>G.current?"right":"left",G.current=e.clientX)}))})})})})})})});ed.displayName=eu;var ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"group",...r,ref:t})});ev.displayName="MenuGroup";var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{...r,ref:t})});eg.displayName="MenuLabel";var ey="MenuItem",ew="menu.itemSelect",ex=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,s=r.useRef(null),c=et(ey,e.__scopeMenu),d=ec(ey,e.__scopeMenu),f=(0,i.s)(t,s),p=r.useRef(!1);return(0,w.jsx)(eb,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&B.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...s}=e,c=ec(ey,n),d=Z(n),f=r.useRef(null),p=(0,i.s)(t,f),[m,h]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[s.children]),(0,w.jsx)(V.ItemSlot,{scope:n,disabled:a,textValue:l??v,children:(0,w.jsx)(P,{asChild:!0,...d,focusable:!a,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eU(e=>{a?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eU(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eE=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,w.jsx)(ej,{scope:e.__scopeMenu,checked:n,children:(0,w.jsx)(ex,{role:"menuitemcheckbox","aria-checked":eG(n)?"mixed":n,...i,ref:t,"data-state":eK(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!eG(n)||!n),{checkForDefaultPrevented:!1})})})});eE.displayName="MenuCheckboxItem";var eC="MenuRadioGroup",[eR,eS]=q(eC,{value:void 0,onValueChange:()=>{}}),eM=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,y.c)(r);return(0,w.jsx)(eR,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,w.jsx)(ev,{...o,ref:t})})});eM.displayName=eC;var ek="MenuRadioItem",eT=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=eS(ek,e.__scopeMenu),a=n===i.value;return(0,w.jsx)(ej,{scope:e.__scopeMenu,checked:a,children:(0,w.jsx)(ex,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eK(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});eT.displayName=ek;var eA="MenuItemIndicator",[ej,eD]=q(eA,{checked:!1}),eN=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eD(eA,n);return(0,w.jsx)(g.C,{present:r||eG(i.checked)||!0===i.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":eK(i.checked)})})});eN.displayName=eA;var eP=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eP.displayName="MenuSeparator";var eL=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=Y(n);return(0,w.jsx)(h.i3,{...o,...r,ref:t})});eL.displayName="MenuArrow";var[eO,eI]=q("MenuSub"),e_="MenuSubTrigger",eF=r.forwardRef((e,t)=>{let n=J(e_,e.__scopeMenu),a=et(e_,e.__scopeMenu),l=eI(e_,e.__scopeMenu),u=ec(e_,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,w.jsx)(er,{asChild:!0,...f,children:(0,w.jsx)(eb,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eH(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eU(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eU(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&G[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eF.displayName=e_;var eB="MenuSubContent",eW=r.forwardRef((e,t)=>{let n=ea(eu,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=J(eu,e.__scopeMenu),s=et(eu,e.__scopeMenu),c=eI(eB,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,w.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:a||u.open,children:(0,w.jsx)(V.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eh,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=K[s.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eH(e){return e?"open":"closed"}function eG(e){return"indeterminate"===e}function eK(e){return eG(e)?"indeterminate":e?"checked":"unchecked"}function eU(e){return t=>"mouse"===t.pointerType?e(t):void 0}eW.displayName=eB;var eV="DropdownMenu",[ez,e$]=(0,a.A)(eV,[X]),eq=X(),[eX,eY]=ez(eV),eZ=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:s=!0}=e,c=eq(t),d=r.useRef(null),[f,p]=(0,l.i)({prop:i,defaultProp:a??!1,onChange:u,caller:eV});return(0,w.jsx)(eX,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,w.jsx)(en,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};eZ.displayName=eV;var eQ="DropdownMenuTrigger",eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eY(eQ,n),s=eq(n);return(0,w.jsx)(er,{asChild:!0,...s,children:(0,w.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eJ.displayName=eQ;var e0=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eq(t);return(0,w.jsx)(el,{...r,...n})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e2=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eY(e1,n),l=eq(n),u=r.useRef(!1);return(0,w.jsx)(ed,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e2.displayName=e1,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(ev,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eg,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var e6=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(ex,{...o,...r,ref:t})});e6.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eE,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eM,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eT,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eN,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eP,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eL,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eF,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eq(n);return(0,w.jsx)(eW,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e5=eZ,e3=eJ,e4=e0,e9=e2,e8=e6},31158:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},31355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(43210),i=n(70569),a=n(14163),l=n(98599),u=n(13495),s=n(60687),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,x=o.useContext(d),[b,E]=o.useState(null),C=b?.ownerDocument??globalThis?.document,[,R]=o.useState({}),S=(0,l.s)(t,e=>E(e)),M=Array.from(x.layers),[k]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),T=M.indexOf(k),A=b?M.indexOf(b):-1,j=x.layersWithOutsidePointerEventsDisabled.size>0,D=A>=T,N=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));!D||n||(h?.(e),g?.(e),e.defaultPrevented||y?.())},C),P=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},C);return function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{A===x.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},C),o.useEffect(()=>{if(b)return n&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(b)),x.layers.add(b),p(),()=>{n&&1===x.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[b,C,n,x]),o.useEffect(()=>()=>{b&&(x.layers.delete(b),x.layersWithOutsidePointerEventsDisabled.delete(b),p())},[b,x]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(a.sG.div,{...w,ref:S,style:{pointerEvents:j?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,P.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,N.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(v),E=(0,a.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>x(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,S.paused]),r.useEffect(()=>{if(w){h.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,c);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(s,c);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(s,E),h.remove(S)},0)}}},[w,b,E,S]);let M=r.useCallback(e=>{if(!n&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,S.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:M})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},38674:(e,t,n)=>{n.d(t,{Mz:()=>e7,i3:()=>tt,UC:()=>te,bL:()=>e8,Bk:()=>ez});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],C=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function M(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function k(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=h(y(t)),u=v(l),s=p(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[l]-=g*(n&&c?-1:1);break;case"end":r[l]+=g*(n&&c?-1:1)}return r}let T=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),s=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=k(s,r,u),f=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:h}=l[n],{x:v,y:g,data:y,reset:w}=await h({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=k(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=S(m),v=l[p?"floating"===d?"reference":"floating":d],g=M(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),x=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=M(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-b.top+h.top)/x.y,bottom:(b.bottom-g.bottom+h.bottom)/x.y,left:(g.left-b.left+h.left)/x.x,right:(b.right-g.right+h.right)/x.x}}function j(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function D(e){return o.some(t=>e[t]>=0)}let N=new Set(["left","top"]);async function P(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),u="y"===y(n),s=N.has(a)?-1:1,c=i&&u?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*c,y:h*s}:{x:h*s,y:v*c}}function L(){return"undefined"!=typeof window}function O(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function _(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!L()&&(e instanceof Node||e instanceof I(e).Node)}function B(e){return!!L()&&(e instanceof Element||e instanceof I(e).Element)}function W(e){return!!L()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function H(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let G=new Set(["inline","contents"]);function K(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!G.has(o)}let U=new Set(["table","td","th"]),V=[":popover-open",":modal"];function z(e){return V.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function Y(e){let t=Z(),n=B(e)?ee(e):e;return $.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||q.some(e=>(n.willChange||"").includes(e))||X.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(O(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||H(e)&&e.host||_(e);return H(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:W(n)&&K(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=I(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],K(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=W(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function ea(e){return B(e)?e:e.contextElement}function el(e){let t=ea(e);if(!W(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let eu=s(0);function es(e){let t=I(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function ec(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=s(1);t&&(r?B(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(a))&&o)?es(a):s(0),c=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=I(a),t=r&&B(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=a,o=eo(n=I(o))}}return M({width:f,height:p,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(_(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function em(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=_(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=Z();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=_(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(_(e));else if(B(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=W(e)?el(e):s(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=es(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return M(r)}function eh(e){return"static"===ee(e).position}function ev(e,t){if(!W(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return _(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(z(e))return r;if(!W(e)){let t=en(e);for(;t&&!J(t);){if(B(t)&&!eh(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,U.has(O(n)))&&eh(o);)o=ev(o,t);return o&&J(o)&&eh(o)&&!Y(o)?r:o||function(e){let t=en(e);for(;W(t)&&!J(t);){if(Y(t))return t;if(z(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=W(t),o=_(t),i="fixed"===n,a=ec(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!i){if(("body"!==O(t)||K(o))&&(l=et(t)),r){let e=ec(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o))}i&&!r&&o&&(u.x=ed(o));let c=!o||r||i?s(0):ef(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=_(r),l=!!t&&z(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=W(r);if((f||!f&&!i)&&(("body"!==O(r)||K(a))&&(u=et(r)),W(r))){let e=ec(r);c=el(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?s(0):ef(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:_,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?z(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==O(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;B(a)&&!J(a);){let t=ee(a),n=Y(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||K(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],s=l.reduce((e,n)=>{let r=em(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},em(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=S(p),w={x:n,y:r},x=h(y(o)),b=v(x),E=await u.getDimensions(d),C="y"===x,R=C?"clientHeight":"clientWidth",M=l.reference[b]+l.reference[x]-w[x]-l.floating[b],k=w[x]-l.reference[x],T=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),A=T?T[R]:0;A&&await (null==u.isElement?void 0:u.isElement(T))||(A=s.floating[R]||l.floating[b]);let j=A/2-E[b]/2-1,D=i(g[C?"top":"left"],j),N=i(g[C?"bottom":"right"],j),P=A-E[b]-N,L=A/2-E[b]/2+(M/2-k/2),O=a(D,i(L,P)),I=!c.arrow&&null!=m(o)&&L!==O&&l.reference[b]/2-(L<D?D:N)-E[b]/2<0,_=I?L<D?L-D:L-P:0;return{[x]:w[x]+_,data:{[x]:O,centerOffset:L-O-_,...I&&{alignmentOffset:_}},reset:I}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return T(e,t,{...o,platform:i})};var eC=n(51215),eR="undefined"!=typeof document?r.useLayoutEffect:function(){};function eS(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eS(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eS(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eM(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ek(e,t){let n=eM(e);return Math.round(t*n)/n}function eT(e){let t=r.useRef(e);return eR(()=>{t.current=e}),t}let eA=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),ej=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await P(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},m=await A(t,c),v=y(p(o)),g=h(v),w=d[g],x=d[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+m[e],r=w-m[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+m[e],r=x-m[t];x=a(n,i(x,r))}let b=s.fn({...t,[g]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},d=y(o),m=h(d),v=c[m],g=c[d],w=f(l,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+x.mainAxis,n=i.reference[m]+i.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var b,E;let e="y"===m?"width":"height",t=N.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[m]:v,[d]:g}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:S=!0,crossAxis:M=!0,fallbackPlacements:k,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:j="none",flipAlignment:D=!0,...N}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let P=p(l),L=y(c),O=p(c)===c,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),_=k||(O||!D?[R(c)]:function(e){let t=R(e);return[w(e),t,w(t)]}(c)),F="none"!==j;!k&&F&&_.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:C;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(c,D,j,I));let B=[c,..._],W=await A(t,N),H=[],G=(null==(r=u.flip)?void 0:r.overflows)||[];if(S&&H.push(W[P]),M){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=R(a)),[a,R(a)]}(l,s,I);H.push(W[e[0]],W[e[1]])}if(G=[...G,{placement:l,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==M||L===y(t)||G.every(e=>e.overflows[0]>0&&y(e.placement)===L)))return{data:{index:e,overflows:G},reset:{placement:t}};let n=null==(i=G.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(a=G.filter(e=>{if(F){let t=y(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:u,rects:s,platform:c,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),g=await A(t,v),w=p(u),x=m(u),b="y"===y(u),{width:E,height:C}=s.floating;"top"===w||"bottom"===w?(o=w,l=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===x?"top":"bottom");let R=C-g.top-g.bottom,S=E-g.left-g.right,M=i(C-g[o],R),k=i(E-g[l],S),T=!t.middlewareData.shift,j=M,D=k;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=R),T&&!x){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):j=C-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await h({...t,availableWidth:D,availableHeight:j});let N=await c.getDimensions(d.floating);return E!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=j(await A(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:D(e)}}}case"escaped":{let e=j(await A(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:D(e)}}}default:return{}}}}}(e),options:[e,t]}),eI=(e,t)=>({...eA(e),options:[e,t]});var e_=n(14163),eF=n(60687),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eF.jsx)(e_.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var eW=n(98599),eH=n(11273),eG=n(13495),eK=n(66156),eU="Popper",[eV,ez]=(0,eH.A)(eU),[e$,eq]=eV(eU),eX=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eF.jsx)(e$,{scope:t,anchor:o,onAnchorChange:i,children:n})};eX.displayName=eU;var eY="PopperAnchor",eZ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eq(eY,n),l=r.useRef(null),u=(0,eW.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eF.jsx)(e_.sG.div,{...i,ref:u})});eZ.displayName=eY;var eQ="PopperContent",[eJ,e0]=eV(eQ),e1=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:s="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,x=eq(eQ,n),[b,E]=r.useState(null),C=(0,eW.s)(t,e=>E(e)),[R,S]=r.useState(null),M=function(e){let[t,n]=r.useState(void 0);return(0,eK.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(R),k=M?.width??0,T=M?.height??0,A="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},j=Array.isArray(p)?p:[p],D=j.length>0,N={padding:A,boundary:j.filter(e3),altBoundary:D},{refs:P,floatingStyles:L,placement:O,isPositioned:I,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);eS(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=a||h,E=l||g,C=r.useRef(null),R=r.useRef(null),S=r.useRef(d),M=null!=s,k=eT(s),T=eT(i),A=eT(c),j=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),eE(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};D.current&&!eS(S.current,t)&&(S.current=t,eC.flushSync(()=>{f(t)}))})},[p,t,n,T,A]);eR(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);eR(()=>(D.current=!0,()=>{D.current=!1}),[]),eR(()=>{if(b&&(C.current=b),E&&(R.current=E),b&&E){if(k.current)return k.current(b,E,j);j()}},[b,E,j,k,M]);let N=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:x}),[w,x]),P=r.useMemo(()=>({reference:b,floating:E}),[b,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!P.floating)return e;let t=ek(P.floating,d.x),r=ek(P.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eM(P.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,P.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:j,refs:N,elements:P,floatingStyles:L}),[d,j,N,P,L])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=ea(e),m=l||s?[...p?er(p):[],...er(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,o=_(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function s(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(c||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ex(f,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?ec(e):null;return f&&function t(){let r=ec(e);y&&!ex(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:x.anchor},middleware:[ej({mainAxis:l+T,alignmentAxis:c}),f&&eD({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eN():void 0,...N}),f&&eP({...N}),eL({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eI({element:R,padding:d}),e4({arrowWidth:k,arrowHeight:T}),v&&eO({strategy:"referenceHidden",...N})]}),[B,W]=e9(O),H=(0,eG.c)(y);(0,eK.N)(()=>{I&&H?.()},[I,H]);let G=F.arrow?.x,K=F.arrow?.y,U=F.arrow?.centerOffset!==0,[V,z]=r.useState();return(0,eK.N)(()=>{b&&z(window.getComputedStyle(b).zIndex)},[b]),(0,eF.jsx)("div",{ref:P.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:I?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:V,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(eJ,{scope:n,placedSide:B,onArrowChange:S,arrowX:G,arrowY:K,shouldHideArrow:U,children:(0,eF.jsx)(e_.sG.div,{"data-side":B,"data-align":W,...w,ref:C,style:{...w.style,animation:I?void 0:"none"}})})})});e1.displayName=eQ;var e2="PopperArrow",e6={top:"bottom",right:"left",bottom:"top",left:"right"},e5=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e0(e2,n),i=e6[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e3(e){return null!==e}e5.displayName=e2;var e4=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,s]=e9(n),c={start:"0%",center:"50%",end:"100%"}[s],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===u?(p=i?c:`${d}px`,m=`${-l}px`):"top"===u?(p=i?c:`${d}px`,m=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,m=i?c:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,m=i?c:`${f}px`),{data:{x:p,y:m}}}});function e9(e){let[t,n="center"]=e.split("-");return[t,n]}var e8=eX,e7=eZ,te=e1,tt=e5},40228:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>V});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(43210)),l="right-scroll-bar-position",u="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),m=function(){},h=a.forwardRef(function(e,t){var n,r,l,u,f=a.useRef(null),h=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=h[0],g=h[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,C=e.shards,R=e.sideCar,S=e.noRelative,M=e.noIsolation,k=e.inert,T=e.allowPinchZoom,A=e.as,j=e.gapMode,D=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[f,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,u=l.facade,c(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(u,n)},[n]),u),P=o(o({},D),v);return a.createElement(a.Fragment,null,E&&a.createElement(R,{sideCar:p,removeScrollBar:b,shards:C,noRelative:S,noIsolation:M,inert:k,setCallbacks:g,allowPinchZoom:!!T,lockRef:f,gapMode:j}),y?a.cloneElement(a.Children.only(w),o(o({},P),{ref:N})):a.createElement(void 0===A?"div":A,o({},P,{className:x,ref:N}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:l};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=w(),S="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},T=function(){a.useEffect(function(){return document.body.setAttribute(S,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(R,{styles:M(i,!t,o,n?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){j=!1}var N=!!j&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,s=t.contains(u),c=!1,d=l>0,f=0,p=0;do{if(!u)break;var m=I(e,u),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&O(e,u)&&(f+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},H=0,G=[];let K=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(H++)[0],i=a.useState(w)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=F(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=L(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?B(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,N),document.addEventListener("touchmove",s,N),document.addEventListener("touchstart",d,N),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,N),document.removeEventListener("touchmove",s,N),document.removeEventListener("touchstart",d,N)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var U=a.forwardRef(function(e,t){return a.createElement(h,o({},e,{ref:t,sideCar:K}))});U.classNames=h.classNames;let V=U},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),o=n(98599),i=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=l(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},53186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]])},63376:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},s=function(e,t,n,r){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],f=new Set,p=new Set(s),m=function(e){!(!e||f.has(e))&&(f.add(e),m(e.parentNode))};s.forEach(m);var h=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(c.get(e)||0)+1;o.set(e,l),c.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),s(o,i,n,"aria-hidden")):function(){return null}}},65551:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[s,e,l,u])]}Symbol("RADIX:SYNC_STATE")},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},69024:(e,t,n)=>{n.d(t,{Qg:()=>a,bL:()=>u});var r=n(43210),o=n(14163),i=n(60687),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...a,...e.style}}));l.displayName="VisuallyHidden";var u=l},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},78005:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-check",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},78122:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},80462:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},91413:(e,t,n)=>{n.d(t,{UC:()=>eD,In:()=>eA,q7:()=>eP,VF:()=>eO,p4:()=>eL,ZL:()=>ej,bL:()=>eM,wn:()=>e_,PP:()=>eI,l9:()=>ek,WT:()=>eT,LM:()=>eN});var r=n(43210),o=n(51215);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(70569),l=n(9510),u=n(98599),s=n(11273),c=n(43),d=n(31355),f=n(1359),p=n(32547),m=n(96963),h=n(38674),v=n(25028),g=n(14163),y=n(8730),w=n(13495),x=n(65551),b=n(66156),E=n(69024),C=n(63376),R=n(42247),S=n(60687),M=[" ","Enter","ArrowUp","ArrowDown"],k=[" ","Enter"],T="Select",[A,j,D]=(0,l.N)(T),[N,P]=(0,s.A)(T,[D,h.Bk]),L=(0,h.Bk)(),[O,I]=N(T),[_,F]=N(T),B=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:a,value:l,defaultValue:u,onValueChange:s,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:y}=e,w=L(t),[b,E]=r.useState(null),[C,R]=r.useState(null),[M,k]=r.useState(!1),j=(0,c.jH)(d),[D,N]=(0,x.i)({prop:o,defaultProp:i??!1,onChange:a,caller:T}),[P,I]=(0,x.i)({prop:l,defaultProp:u,onChange:s,caller:T}),F=r.useRef(null),B=!b||y||!!b.closest("form"),[W,H]=r.useState(new Set),G=Array.from(W).map(e=>e.props.value).join(";");return(0,S.jsx)(h.bL,{...w,children:(0,S.jsxs)(O,{required:g,scope:t,trigger:b,onTriggerChange:E,valueNode:C,onValueNodeChange:R,valueNodeHasChildren:M,onValueNodeHasChildrenChange:k,contentId:(0,m.B)(),value:P,onValueChange:I,open:D,onOpenChange:N,dir:j,triggerPointerDownPosRef:F,disabled:v,children:[(0,S.jsx)(A.Provider,{scope:t,children:(0,S.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{H(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{H(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,S.jsxs)(eE,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>I(e.target.value),disabled:v,form:y,children:[void 0===P?(0,S.jsx)("option",{value:""}):null,Array.from(W)]},G):null]})})};B.displayName=T;var W="SelectTrigger",H=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,l=L(n),s=I(W,n),c=s.disabled||o,d=(0,u.s)(t,s.onTriggerChange),f=j(n),p=r.useRef("touch"),[m,v,y]=eR(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eS(t,e,n);void 0!==r&&s.onValueChange(r.value)}),w=e=>{c||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(h.Mz,{asChild:!0,...l,children:(0,S.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eC(s.value)?"":void 0,...i,ref:d,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&M.includes(e.key)&&(w(),e.preventDefault())})})})});H.displayName=W;var G="SelectValue",K=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...l}=e,s=I(G,n),{onValueNodeHasChildrenChange:c}=s,d=void 0!==i,f=(0,u.s)(t,s.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,S.jsx)(g.sG.span,{...l,ref:f,style:{pointerEvents:"none"},children:eC(s.value)?(0,S.jsx)(S.Fragment,{children:a}):i})});K.displayName=G;var U=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});U.displayName="SelectIcon";var V=e=>(0,S.jsx)(v.Z,{asChild:!0,...e});V.displayName="SelectPortal";var z="SelectContent",$=r.forwardRef((e,t)=>{let n=I(z,e.__scopeSelect),[i,a]=r.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,S.jsx)(Z,{...e,ref:t}):i?o.createPortal((0,S.jsx)(q,{scope:e.__scopeSelect,children:(0,S.jsx)(A.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),i):null});$.displayName=z;var[q,X]=N(z),Y=(0,y.TL)("SelectContent.RemoveScroll"),Z=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:s,side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:E,...M}=e,k=I(z,n),[T,A]=r.useState(null),[D,N]=r.useState(null),P=(0,u.s)(t,e=>A(e)),[L,O]=r.useState(null),[_,F]=r.useState(null),B=j(n),[W,H]=r.useState(!1),G=r.useRef(!1);r.useEffect(()=>{if(T)return(0,C.Eq)(T)},[T]),(0,f.Oh)();let K=r.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&D&&(D.scrollTop=0),n===r&&D&&(D.scrollTop=D.scrollHeight),n?.focus(),document.activeElement!==o))return},[B,D]),U=r.useCallback(()=>K([L,T]),[K,L,T]);r.useEffect(()=>{W&&U()},[W,U]);let{onOpenChange:V,triggerPointerDownPosRef:$}=k;r.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-($.current?.x??0)),y:Math.abs(Math.round(t.pageY)-($.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,$]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[X,Z]=eR(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eS(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==k.value&&k.value===t||r)&&(O(e),r&&(G.current=!0))},[k.value]),et=r.useCallback(()=>T?.focus(),[T]),en=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==k.value&&k.value===t||r)&&F(e)},[k.value]),er="popper"===o?J:Q,eo=er===J?{side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:E}:{};return(0,S.jsx)(q,{scope:n,content:T,viewport:D,onViewportChange:N,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:U,selectedItemText:_,position:o,isPositioned:W,searchRef:X,children:(0,S.jsx)(R.A,{as:Y,allowPinchZoom:!0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{k.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...M,...eo,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...M.style},onKeyDown:(0,a.m)(M.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});Z.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...a}=e,l=I(z,n),s=X(z,n),[c,d]=r.useState(null),[f,p]=r.useState(null),m=(0,u.s)(t,e=>p(e)),h=j(n),v=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:E,focusSelectedItem:C}=s,R=r.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&w&&x&&E){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=E.getBoundingClientRect();if("rtl"!==l.dir){let o=r.left-t.left,a=n.left-o,l=e.left-a,u=e.width+l,s=Math.max(u,t.width),d=i(a,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.left=d+"px"}else{let o=t.right-r.right,a=window.innerWidth-n.right-o,l=window.innerWidth-e.right-a,u=e.width+l,s=Math.max(u,t.width),d=i(a,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=u+"px",c.style.right=d+"px"}let a=h(),u=window.innerHeight-20,s=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+m+s+parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),S=parseInt(C.paddingBottom,10),M=e.top+e.height/2-10,k=x.offsetHeight/2,T=p+m+(x.offsetTop+k);if(T<=M){let e=a.length>0&&x===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(u-M,k+(e?S:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);c.style.height=T+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;c.style.top="0px";let t=Math.max(M,p+w.offsetTop+(e?R:0)+k);c.style.height=t+(y-T)+"px",w.scrollTop=T-M+w.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=u+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[h,l.trigger,l.valueNode,c,f,w,x,E,l.dir,o]);(0,b.N)(()=>R(),[R]);let[M,k]=r.useState();(0,b.N)(()=>{f&&k(window.getComputedStyle(f).zIndex)},[f]);let T=r.useCallback(e=>{e&&!0===y.current&&(R(),C?.(),y.current=!1)},[R,C]);return(0,S.jsx)(ee,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:T,children:(0,S.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:(0,S.jsx)(g.sG.div,{...a,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var J=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,a=L(n);return(0,S.jsx)(h.UC,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="SelectPopperPosition";var[ee,et]=N(z,{}),en="SelectViewport",er=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,l=X(en,n),s=et(en,n),c=(0,u.s)(t,l.onViewportChange),d=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(A.Slot,{scope:n,children:(0,S.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});er.displayName=en;var eo="SelectGroup",[ei,ea]=N(eo);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,m.B)();return(0,S.jsx)(ei,{scope:n,id:o,children:(0,S.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=eo;var el="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(el,n);return(0,S.jsx)(g.sG.div,{id:o.id,...r,ref:t})}).displayName=el;var eu="SelectItem",[es,ec]=N(eu),ed=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:l,...s}=e,c=I(eu,n),d=X(eu,n),f=c.value===o,[p,h]=r.useState(l??""),[v,y]=r.useState(!1),w=(0,u.s)(t,e=>d.itemRefCallback?.(e,o,i)),x=(0,m.B)(),b=r.useRef("touch"),E=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(es,{scope:n,value:o,disabled:i,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,S.jsx)(A.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,S.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:w,onFocus:(0,a.m)(s.onFocus,()=>y(!0)),onBlur:(0,a.m)(s.onBlur,()=>y(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(k.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});ed.displayName=eu;var ef="SelectItemText",ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...l}=e,s=I(ef,n),c=X(ef,n),d=ec(ef,n),f=F(ef,n),[p,m]=r.useState(null),h=(0,u.s)(t,e=>m(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,y=r.useMemo(()=>(0,S.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(g.sG.span,{id:d.textId,...l,ref:h}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(l.children,s.valueNode):null]})});ep.displayName=ef;var em="SelectItemIndicator",eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ec(em,n).isSelected?(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eh.displayName=em;var ev="SelectScrollUpButton",eg=r.forwardRef((e,t)=>{let n=X(ev,e.__scopeSelect),o=et(ev,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,u.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(ex,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eg.displayName=ev;var ey="SelectScrollDownButton",ew=r.forwardRef((e,t)=>{let n=X(ey,e.__scopeSelect),o=et(ey,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,u.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(ex,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ew.displayName=ey;var ex=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,l=X("SelectScrollButton",n),u=r.useRef(null),s=j(n),c=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{let e=s().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[s]),(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{l.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eb="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=L(n),i=I(eb,n),a=X(eb,n);return i.open&&"popper"===a.position?(0,S.jsx)(h.i3,{...o,...r,ref:t}):null}).displayName=eb;var eE=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),a=(0,u.s)(o,i),l=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[l,t]),(0,S.jsx)(g.sG.select,{...n,style:{...E.Qg,...n.style},ref:a,defaultValue:t})});function eC(e){return""===e||void 0===e}function eR(e){let t=(0,w.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,a]}function eS(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=e,o=Math.max(a,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}eE.displayName="SelectBubbleInput";var eM=B,ek=H,eT=K,eA=U,ej=V,eD=$,eN=er,eP=ed,eL=ep,eO=eh,eI=eg,e_=ew},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};