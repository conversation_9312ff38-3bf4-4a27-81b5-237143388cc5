module.exports = {

"[project]/.next-internal/server/app/api/update-cases/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getAllCases": (()=>getAllCases),
    "getCaseByNumber": (()=>getCaseByNumber),
    "getCaseStats": (()=>getCaseStats),
    "getCasesByTaluka": (()=>getCasesByTaluka),
    "initializeDatabase": (()=>initializeDatabase),
    "updateCaseField": (()=>updateCaseField),
    "upsertCases": (()=>upsertCases)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$neondatabase$2f$serverless$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@neondatabase/serverless/index.mjs [app-route] (ecmascript)");
;
// Initialize Neon client
const sql = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$neondatabase$2f$serverless$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["neon"])(process.env.DATABASE_URL);
async function initializeDatabase() {
    try {
        await sql`
      CREATE TABLE IF NOT EXISTS legal_cases (
        id SERIAL PRIMARY KEY,
        sr_no VARCHAR(50),
        case_number VARCHAR(100) NOT NULL,
        case_type VARCHAR(100),
        applicant_name VARCHAR(255) NOT NULL,
        respondent_name VARCHAR(255) NOT NULL,
        received VARCHAR(100),
        next_date DATE,
        status TEXT,
        remarks TEXT,
        taluka VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
        // Add missing columns if they don't exist
        try {
            await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS case_type VARCHAR(100)`;
            await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS next_date DATE`;
            await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS received VARCHAR(100)`;
            // Change status to TEXT to allow longer status messages
            await sql`ALTER TABLE legal_cases ALTER COLUMN status TYPE TEXT`;
        } catch (alterError) {
            console.log('Some columns may already exist:', alterError.message);
        }
        // Create index for better performance
        await sql`
      CREATE INDEX IF NOT EXISTS idx_legal_cases_taluka ON legal_cases(taluka)
    `;
        await sql`
      CREATE INDEX IF NOT EXISTS idx_legal_cases_case_number ON legal_cases(case_number)
    `;
        console.log('Database initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing database:', error);
        return false;
    }
}
async function upsertCases(cases) {
    try {
        // Clear existing cases (for now - in production you might want to do incremental updates)
        await sql`DELETE FROM legal_cases`;
        // Insert new cases
        for (const case_ of cases){
            await sql`
        INSERT INTO legal_cases (
          sr_no, case_number, case_type, applicant_name, respondent_name,
          received, next_date, status, remarks, taluka
        ) VALUES (
          ${case_.sr_no}, ${case_.case_number}, ${case_.case_type || 'अपील'},
          ${case_.applicant_name}, ${case_.respondent_name},
          ${case_.received || 'प्राप्त'}, ${case_.next_date || '2025-07-17'},
          ${case_.status}, ${case_.remarks}, ${case_.taluka}
        )
      `;
        }
        console.log(`Successfully inserted ${cases.length} cases`);
        return {
            success: true,
            count: cases.length
        };
    } catch (error) {
        console.error('Error upserting cases:', error);
        return {
            success: false,
            error: error.message
        };
    }
}
async function getAllCases() {
    try {
        const cases = await sql`
      SELECT * FROM legal_cases
      ORDER BY created_at DESC
    `;
        return cases;
    } catch (error) {
        console.error('Error fetching cases:', error);
        return [];
    }
}
async function getCasesByTaluka(taluka) {
    try {
        const cases = await sql`
      SELECT * FROM legal_cases
      WHERE taluka = ${taluka}
      ORDER BY created_at DESC
    `;
        return cases;
    } catch (error) {
        console.error('Error fetching cases by taluka:', error);
        return [];
    }
}
async function getCaseStats() {
    try {
        const stats = await sql`
      SELECT
        taluka,
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,
        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases
      FROM legal_cases
      GROUP BY taluka
    `;
        const totalStats = await sql`
      SELECT
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,
        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases
      FROM legal_cases
    `;
        return {
            byTaluka: stats,
            total: totalStats[0]
        };
    } catch (error) {
        console.error('Error fetching case stats:', error);
        return {
            byTaluka: [],
            total: {
                total_cases: 0,
                received_cases: 0,
                pending_cases: 0
            }
        };
    }
}
async function updateCaseField(caseNumber, field, value) {
    try {
        let query;
        switch(field){
            case 'status':
                query = sql`
          UPDATE legal_cases
          SET status = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            case 'received':
                query = sql`
          UPDATE legal_cases
          SET received = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            case 'next_date':
                query = sql`
          UPDATE legal_cases
          SET next_date = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            case 'case_type':
                query = sql`
          UPDATE legal_cases
          SET case_type = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            default:
                throw new Error(`Invalid field: ${field}`);
        }
        const result = await query;
        console.log(`Updated case ${caseNumber} field ${field} to ${value}`);
        return {
            success: true,
            updated: result.count || 0
        };
    } catch (error) {
        console.error(`Error updating case ${caseNumber} field ${field}:`, error);
        return {
            success: false,
            error: error.message
        };
    }
}
async function getCaseByNumber(caseNumber) {
    try {
        const cases = await sql`
      SELECT * FROM legal_cases
      WHERE case_number = ${caseNumber}
      LIMIT 1
    `;
        return cases.length > 0 ? cases[0] : null;
    } catch (error) {
        console.error('Error fetching case by number:', error);
        return null;
    }
}
}}),
"[project]/app/api/update-cases/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    // Add CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    };
    try {
        console.log('=== API Request Started ===');
        console.log('Request method:', request.method);
        console.log('Request URL:', request.url);
        console.log('Content-Type:', request.headers.get('content-type'));
        // Get the form data from the request
        const formData = await request.formData();
        console.log('Form data parsed successfully');
        // Debug: Log all form field names and their types
        console.log('Form data keys:', Array.from(formData.keys()));
        for (const [key, value] of formData.entries()){
            console.log(`Field "${key}":`, typeof value, value instanceof File ? `File: ${value.name}, size: ${value.size}` : `Value: ${String(value).substring(0, 100)}...`);
        }
        // Check if we have both key and value fields (from your automation)
        const allValues = formData.getAll('key').concat(formData.getAll('value'));
        console.log('All key/value entries:', allValues.length);
        // First, check if 'key' contains CSV text data (which is what your automation sends)
        const keyValue = formData.get('key');
        if (keyValue && typeof keyValue === 'string') {
            console.log('Found CSV text in key field, processing directly');
            console.log('CSV text preview:', keyValue.substring(0, 200));
            try {
                return await processCsvText(keyValue);
            } catch (csvError) {
                console.error('Error processing CSV text from key field:', csvError);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: "Failed to process CSV text",
                    details: csvError.message
                }, {
                    status: 500
                });
            }
        }
        // Also check 'value' field (your automation might be sending both)
        const valueField = formData.get('value');
        if (valueField && typeof valueField === 'string') {
            console.log('Found CSV text in value field, processing directly');
            console.log('CSV text preview:', valueField.substring(0, 200));
            try {
                return await processCsvText(valueField);
            } catch (csvError) {
                console.error('Error processing CSV text from value field:', csvError);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: "Failed to process CSV text",
                    details: csvError.message
                }, {
                    status: 500
                });
            }
        }
        // If key is a file, process it as a file
        if (keyValue && keyValue instanceof File) {
            console.log('Found file in key field:', keyValue.name, 'size:', keyValue.size);
            try {
                const bytes = await keyValue.arrayBuffer();
                const csvText = new TextDecoder('utf-8').decode(bytes);
                return await processCsvText(csvText);
            } catch (fileError) {
                console.error('Error reading key file:', fileError);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: "Failed to read uploaded file from key field"
                }, {
                    status: 500
                });
            }
        }
        // Look for a file in the 'file' field as fallback
        const file = formData.get('file');
        if (file && file instanceof File) {
            console.log('Found file in file field:', file.name, 'size:', file.size);
            try {
                const bytes = await file.arrayBuffer();
                const csvText = new TextDecoder('utf-8').decode(bytes);
                return await processCsvText(csvText);
            } catch (fileError) {
                console.error('Error reading file field:', fileError);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: "Failed to read uploaded file from file field"
                }, {
                    status: 500
                });
            }
        }
        // Check if CSV data was sent as text in csvData field
        const csvData = formData.get('csvData');
        if (csvData) {
            console.log('Found CSV data in csvData field');
            return await processCsvText(csvData);
        }
        console.log('No CSV data found in any field');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "No CSV file or data provided"
        }, {
            status: 400
        });
    } catch (error) {
        console.error("Error processing CSV:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to process CSV data",
            details: error instanceof Error ? error.message : "Unknown error"
        }, {
            status: 500,
            headers
        });
    }
}
// Helper function to process CSV text
async function processCsvText(csvText) {
    try {
        // Parse CSV
        const lines = csvText.trim().split("\n");
        if (lines.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Empty CSV data"
            }, {
                status: 400
            });
        }
        const headers = lines[0].split(",").map((h)=>h.trim().replace(/"/g, ""));
        const cases = [];
        for(let i = 1; i < lines.length; i++){
            const values = lines[i].split(",").map((v)=>v.trim().replace(/"/g, ""));
            if (values.length === headers.length) {
                const caseData = {};
                headers.forEach((header, index)=>{
                    caseData[header] = values[index];
                });
                // Auto-detect and assign location based on CSV content
                const detectedLocation = detectLocation(caseData, csvText);
                caseData.taluka = detectedLocation;
                cases.push(caseData);
            }
        }
        // Categorize cases by location
        const igatpuriCases = cases.filter((c)=>c.taluka === "Igatpuri");
        const trimbakeshwarCases = cases.filter((c)=>c.taluka === "Trimbakeshwar");
        // Save to localStorage for each location
        await saveCasesToStorage(igatpuriCases, trimbakeshwarCases);
        console.log(`Processed ${cases.length} cases from CSV - Igatpuri: ${igatpuriCases.length}, Trimbakeshwar: ${trimbakeshwarCases.length}`);
        // Initialize database and save cases
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeDatabase"])();
            // Convert cases to database format with new structure
            const dbCases = cases.map((case_)=>({
                    sr_no: case_["Sr No"] || "",
                    case_number: case_["Case Number"] || "",
                    case_type: case_["Case Type"] || "",
                    applicant_name: case_["Appellant"] || case_["Applicant Name"] || "",
                    respondent_name: case_["Respondent"] || case_["Respondent Name"] || "",
                    received: case_["Received"] || "",
                    next_date: case_["Next Date"] || "",
                    status: case_["Status"] || "",
                    remarks: case_["Remarks"] || "",
                    taluka: case_.taluka || "Unknown"
                }));
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["upsertCases"])(dbCases);
            if (!result.success) {
                console.error('Error saving cases to database:', result.error);
            } else {
                console.log('Cases saved to database successfully');
            }
        } catch (dbError) {
            console.error('Error with database operation:', dbError);
        // Continue anyway - don't fail the API call
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: `Successfully processed ${cases.length} cases`,
            breakdown: {
                igatpuri: igatpuriCases.length,
                trimbakeshwar: trimbakeshwarCases.length
            },
            detectedLocation: cases.length > 0 ? cases[0].taluka : "Unknown",
            cases: cases.slice(0, 5),
            totalCases: cases.length,
            headers: headers
        }, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            }
        });
    } catch (error) {
        console.error("Error parsing CSV:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to parse CSV data",
            details: error instanceof Error ? error.message : "Unknown error"
        }, {
            status: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            }
        });
    }
}
// Helper function to detect location based on CSV content
function detectLocation(caseData, csvText) {
    // Method 1: Check case numbers or IDs for patterns (most specific)
    const caseNumber = caseData['Case Number'] || caseData['caseNumber'] || caseData['Case ID'] || caseData['caseId'];
    if (caseNumber) {
        const caseNum = caseNumber.toLowerCase();
        if (caseNum.includes('tmb') || caseNum.includes('trimbakeshwar')) return "Trimbakeshwar";
        if (caseNum.includes('igt') || caseNum.includes('igatpuri')) return "Igatpuri";
    }
    // Method 2: Check each field individually for location info (case-by-case basis)
    for (const [key, value] of Object.entries(caseData)){
        const val = value?.toLowerCase() || '';
        // Check for Devanagari/Marathi text
        if (val.includes('त्र्यंबकेश्वर') || val.includes('trimbakeshwar')) return "Trimbakeshwar";
        if (val.includes('इगतपुरी') || val.includes('igatpuri')) return "Igatpuri";
    }
    // Method 3: Check if there's already a taluka/location field
    const locationFields = [
        'taluka',
        'location',
        'court',
        'office',
        'jurisdiction'
    ];
    for (const field of locationFields){
        const value = caseData[field]?.toLowerCase();
        if (value) {
            if (value.includes('त्र्यंबकेश्वर') || value.includes('trimbakeshwar')) return "Trimbakeshwar";
            if (value.includes('इगतपुरी') || value.includes('igatpuri')) return "Igatpuri";
        }
    }
    // Method 4: Check filename or overall CSV content (last resort)
    const textToCheck = csvText.toLowerCase();
    if ((textToCheck.includes('त्र्यंबकेश्वर') || textToCheck.includes('trimbakeshwar')) && !(textToCheck.includes('इगतपुरी') || textToCheck.includes('igatpuri'))) return "Trimbakeshwar";
    if ((textToCheck.includes('इगतपुरी') || textToCheck.includes('igatpuri')) && !(textToCheck.includes('त्र्यंबकेश्वर') || textToCheck.includes('trimbakeshwar'))) return "Igatpuri";
    // Default: If no location detected, return Igatpuri as default
    return "Igatpuri" // Default location
    ;
}
// Helper function to save cases to storage (simulating database)
async function saveCasesToStorage(igatpuriCases, trimbakeshwarCases) {
    // In a real application, this would save to a database
    // For now, we'll just log the categorization
    console.log(`Saving cases - Igatpuri: ${igatpuriCases.length}, Trimbakeshwar: ${trimbakeshwarCases.length}`);
// You could implement actual storage logic here
// For example, saving to different database tables or files
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
}
async function GET() {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeDatabase"])();
        const cases = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAllCases"])();
        const stats = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCaseStats"])();
        // Convert database format back to dashboard format (CaseData interface)
        const dashboardCases = cases.map((case_)=>({
                date: case_.created_at || new Date().toISOString(),
                caseType: case_.case_type || "अपील",
                caseNumber: case_.case_number,
                appellant: case_.applicant_name,
                respondent: case_.respondent_name,
                received: case_.received || "प्राप्त",
                nextDate: case_.next_date || "2025-07-17",
                status: case_.status || "",
                taluka: case_.taluka,
                filedDate: case_.created_at || new Date().toISOString(),
                lastUpdate: case_.updated_at || case_.created_at || new Date().toISOString()
            }));
        const breakdown = {
            igatpuri: stats.byTaluka.find((s)=>s.taluka === 'Igatpuri')?.total_cases || 0,
            trimbakeshwar: stats.byTaluka.find((s)=>s.taluka === 'Trimbakeshwar')?.total_cases || 0
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            cases: dashboardCases,
            lastUpdated: cases.length > 0 ? cases[0].created_at : new Date().toISOString(),
            breakdown: breakdown,
            stats: stats
        }, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            }
        });
    } catch (error) {
        console.error('Error fetching cases from database:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: "Failed to fetch cases from database",
            cases: []
        }, {
            status: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            }
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__3538530f._.js.map