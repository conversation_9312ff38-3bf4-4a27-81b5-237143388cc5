{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/lib/db.ts"], "sourcesContent": ["import { neon } from '@neondatabase/serverless'\n\n// Initialize Neon client\nconst sql = neon(process.env.DATABASE_URL!)\n\nexport interface CaseRecord {\n  id?: number\n  sr_no: string\n  case_number: string\n  case_type?: string\n  applicant_name: string\n  respondent_name: string\n  received?: string\n  next_date?: string\n  status: string\n  remarks: string\n  taluka: string\n  created_at?: string\n  updated_at?: string\n}\n\n// Create tables if they don't exist\nexport async function initializeDatabase() {\n  try {\n    await sql`\n      CREATE TABLE IF NOT EXISTS legal_cases (\n        id SERIAL PRIMARY KEY,\n        sr_no VARCHAR(50),\n        case_number VARCHAR(100) NOT NULL,\n        case_type VARCHAR(100),\n        applicant_name VARCHAR(255) NOT NULL,\n        respondent_name VARCHAR(255) NOT NULL,\n        received VARCHAR(100),\n        next_date DATE,\n        status TEXT,\n        remarks TEXT,\n        taluka VARCHAR(50) NOT NULL,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `\n\n    // Add missing columns if they don't exist\n    try {\n      await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS case_type VARCHAR(100)`\n      await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS next_date DATE`\n      await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS received VARCHAR(100)`\n      // Change status to TEXT to allow longer status messages\n      await sql`ALTER TABLE legal_cases ALTER COLUMN status TYPE TEXT`\n    } catch (alterError) {\n      console.log('Some columns may already exist:', alterError.message)\n    }\n\n    // Create index for better performance\n    await sql`\n      CREATE INDEX IF NOT EXISTS idx_legal_cases_taluka ON legal_cases(taluka)\n    `\n\n    await sql`\n      CREATE INDEX IF NOT EXISTS idx_legal_cases_case_number ON legal_cases(case_number)\n    `\n\n    console.log('Database initialized successfully')\n    return true\n  } catch (error) {\n    console.error('Error initializing database:', error)\n    return false\n  }\n}\n\n// Insert or update cases\nexport async function upsertCases(cases: CaseRecord[]) {\n  try {\n    // Clear existing cases (for now - in production you might want to do incremental updates)\n    await sql`DELETE FROM legal_cases`\n\n    // Insert new cases\n    for (const case_ of cases) {\n      await sql`\n        INSERT INTO legal_cases (\n          sr_no, case_number, case_type, applicant_name, respondent_name,\n          received, next_date, status, remarks, taluka\n        ) VALUES (\n          ${case_.sr_no}, ${case_.case_number}, ${case_.case_type || 'अपील'},\n          ${case_.applicant_name}, ${case_.respondent_name},\n          ${case_.received || 'प्राप्त'}, ${case_.next_date || '2025-07-17'},\n          ${case_.status}, ${case_.remarks}, ${case_.taluka}\n        )\n      `\n    }\n\n    console.log(`Successfully inserted ${cases.length} cases`)\n    return { success: true, count: cases.length }\n  } catch (error) {\n    console.error('Error upserting cases:', error)\n    return { success: false, error: error.message }\n  }\n}\n\n// Get all cases\nexport async function getAllCases(): Promise<CaseRecord[]> {\n  try {\n    const cases = await sql`\n      SELECT * FROM legal_cases\n      ORDER BY created_at DESC\n    `\n    return cases as CaseRecord[]\n  } catch (error) {\n    console.error('Error fetching cases:', error)\n    return []\n  }\n}\n\n// Get cases by taluka\nexport async function getCasesByTaluka(taluka: string): Promise<CaseRecord[]> {\n  try {\n    const cases = await sql`\n      SELECT * FROM legal_cases\n      WHERE taluka = ${taluka}\n      ORDER BY created_at DESC\n    `\n    return cases as CaseRecord[]\n  } catch (error) {\n    console.error('Error fetching cases by taluka:', error)\n    return []\n  }\n}\n\n// Get case statistics\nexport async function getCaseStats() {\n  try {\n    const stats = await sql`\n      SELECT\n        taluka,\n        COUNT(*) as total_cases,\n        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,\n        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases\n      FROM legal_cases\n      GROUP BY taluka\n    `\n\n    const totalStats = await sql`\n      SELECT\n        COUNT(*) as total_cases,\n        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,\n        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases\n      FROM legal_cases\n    `\n\n    return {\n      byTaluka: stats,\n      total: totalStats[0]\n    }\n  } catch (error) {\n    console.error('Error fetching case stats:', error)\n    return { byTaluka: [], total: { total_cases: 0, received_cases: 0, pending_cases: 0 } }\n  }\n}\n\n// Update individual case fields\nexport async function updateCaseField(caseNumber: string, field: string, value: string) {\n  try {\n    let query\n    switch (field) {\n      case 'status':\n        query = sql`\n          UPDATE legal_cases\n          SET status = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      case 'received':\n        query = sql`\n          UPDATE legal_cases\n          SET received = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      case 'next_date':\n        query = sql`\n          UPDATE legal_cases\n          SET next_date = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      case 'case_type':\n        query = sql`\n          UPDATE legal_cases\n          SET case_type = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      default:\n        throw new Error(`Invalid field: ${field}`)\n    }\n\n    const result = await query\n    console.log(`Updated case ${caseNumber} field ${field} to ${value}`)\n    return { success: true, updated: result.count || 0 }\n  } catch (error) {\n    console.error(`Error updating case ${caseNumber} field ${field}:`, error)\n    return { success: false, error: error.message }\n  }\n}\n\n// Get a specific case by case number\nexport async function getCaseByNumber(caseNumber: string): Promise<CaseRecord | null> {\n  try {\n    const cases = await sql`\n      SELECT * FROM legal_cases\n      WHERE case_number = ${caseNumber}\n      LIMIT 1\n    `\n    return cases.length > 0 ? cases[0] as CaseRecord : null\n  } catch (error) {\n    console.error('Error fetching case by number:', error)\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AAmBlC,eAAe;IACpB,IAAI;QACF,MAAM,GAAG,CAAC;;;;;;;;;;;;;;;;IAgBV,CAAC;QAED,0CAA0C;QAC1C,IAAI;YACF,MAAM,GAAG,CAAC,uEAAuE,CAAC;YAClF,MAAM,GAAG,CAAC,+DAA+D,CAAC;YAC1E,MAAM,GAAG,CAAC,sEAAsE,CAAC;YACjF,wDAAwD;YACxD,MAAM,GAAG,CAAC,qDAAqD,CAAC;QAClE,EAAE,OAAO,YAAY;YACnB,QAAQ,GAAG,CAAC,mCAAmC,WAAW,OAAO;QACnE;QAEA,sCAAsC;QACtC,MAAM,GAAG,CAAC;;IAEV,CAAC;QAED,MAAM,GAAG,CAAC;;IAEV,CAAC;QAED,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,eAAe,YAAY,KAAmB;IACnD,IAAI;QACF,0FAA0F;QAC1F,MAAM,GAAG,CAAC,uBAAuB,CAAC;QAElC,mBAAmB;QACnB,KAAK,MAAM,SAAS,MAAO;YACzB,MAAM,GAAG,CAAC;;;;;UAKN,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,SAAS,IAAI,OAAO;UAClE,EAAE,MAAM,cAAc,CAAC,EAAE,EAAE,MAAM,eAAe,CAAC;UACjD,EAAE,MAAM,QAAQ,IAAI,UAAU,EAAE,EAAE,MAAM,SAAS,IAAI,aAAa;UAClE,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;;MAEtD,CAAC;QACH;QAEA,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;QACzD,OAAO;YAAE,SAAS;YAAM,OAAO,MAAM,MAAM;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;;IAGxB,CAAC;QACD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;AACF;AAGO,eAAe,iBAAiB,MAAc;IACnD,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;qBAEP,EAAE,OAAO;;IAE1B,CAAC;QACD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;;;;;;;IAQxB,CAAC;QAED,MAAM,aAAa,MAAM,GAAG,CAAC;;;;;;IAM7B,CAAC;QAED,OAAO;YACL,UAAU;YACV,OAAO,UAAU,CAAC,EAAE;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,UAAU,EAAE;YAAE,OAAO;gBAAE,aAAa;gBAAG,gBAAgB;gBAAG,eAAe;YAAE;QAAE;IACxF;AACF;AAGO,eAAe,gBAAgB,UAAkB,EAAE,KAAa,EAAE,KAAa;IACpF,IAAI;QACF,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,QAAQ,GAAG,CAAC;;uBAEG,EAAE,MAAM;8BACD,EAAE,WAAW;QACnC,CAAC;gBACD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;;yBAEK,EAAE,MAAM;8BACH,EAAE,WAAW;QACnC,CAAC;gBACD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;;0BAEM,EAAE,MAAM;8BACJ,EAAE,WAAW;QACnC,CAAC;gBACD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;;0BAEM,EAAE,MAAM;8BACJ,EAAE,WAAW;QACnC,CAAC;gBACD;YACF;gBACE,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,OAAO;QAC7C;QAEA,MAAM,SAAS,MAAM;QACrB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,WAAW,OAAO,EAAE,MAAM,IAAI,EAAE,OAAO;QACnE,OAAO;YAAE,SAAS;YAAM,SAAS,OAAO,KAAK,IAAI;QAAE;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,WAAW,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE;QACnE,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;AAGO,eAAe,gBAAgB,UAAkB;IACtD,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;0BAEF,EAAE,WAAW;;IAEnC,CAAC;QACD,OAAO,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,GAAiB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/app/api/update-cases/route.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from \"next/server\"\nimport { initializeDatabase, upsertCases, getAllCases, getCaseStats } from \"@/lib/db\"\n\n// This API endpoint will be called by your automation\nexport async function POST(request: NextRequest) {\n  // Add CORS headers\n  const headers = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'POST, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type',\n  }\n  try {\n    console.log('=== API Request Started ===')\n    console.log('Request method:', request.method)\n    console.log('Request URL:', request.url)\n    console.log('Content-Type:', request.headers.get('content-type'))\n\n    // Get the form data from the request\n    const formData = await request.formData()\n    console.log('Form data parsed successfully')\n\n    // Debug: Log all form field names and their types\n    console.log('Form data keys:', Array.from(formData.keys()))\n    for (const [key, value] of formData.entries()) {\n      console.log(`Field \"${key}\":`, typeof value, value instanceof File ? `File: ${value.name}, size: ${value.size}` : `Value: ${String(value).substring(0, 100)}...`)\n    }\n\n    // Check if we have both key and value fields (from your automation)\n    const allValues = formData.getAll('key').concat(formData.getAll('value'))\n    console.log('All key/value entries:', allValues.length)\n\n    // First, check if 'key' contains CSV text data (which is what your automation sends)\n    const keyValue = formData.get('key')\n    if (keyValue && typeof keyValue === 'string') {\n      console.log('Found CSV text in key field, processing directly')\n      console.log('CSV text preview:', keyValue.substring(0, 200))\n      try {\n        return await processCsvText(keyValue)\n      } catch (csvError) {\n        console.error('Error processing CSV text from key field:', csvError)\n        return NextResponse.json({ error: \"Failed to process CSV text\", details: csvError.message }, { status: 500 })\n      }\n    }\n\n    // Also check 'value' field (your automation might be sending both)\n    const valueField = formData.get('value')\n    if (valueField && typeof valueField === 'string') {\n      console.log('Found CSV text in value field, processing directly')\n      console.log('CSV text preview:', valueField.substring(0, 200))\n      try {\n        return await processCsvText(valueField)\n      } catch (csvError) {\n        console.error('Error processing CSV text from value field:', csvError)\n        return NextResponse.json({ error: \"Failed to process CSV text\", details: csvError.message }, { status: 500 })\n      }\n    }\n\n    // If key is a file, process it as a file\n    if (keyValue && keyValue instanceof File) {\n      console.log('Found file in key field:', keyValue.name, 'size:', keyValue.size)\n      try {\n        const bytes = await keyValue.arrayBuffer()\n        const csvText = new TextDecoder('utf-8').decode(bytes)\n        return await processCsvText(csvText)\n      } catch (fileError) {\n        console.error('Error reading key file:', fileError)\n        return NextResponse.json({ error: \"Failed to read uploaded file from key field\" }, { status: 500 })\n      }\n    }\n\n    // Look for a file in the 'file' field as fallback\n    const file = formData.get('file') as File\n    if (file && file instanceof File) {\n      console.log('Found file in file field:', file.name, 'size:', file.size)\n      try {\n        const bytes = await file.arrayBuffer()\n        const csvText = new TextDecoder('utf-8').decode(bytes)\n        return await processCsvText(csvText)\n      } catch (fileError) {\n        console.error('Error reading file field:', fileError)\n        return NextResponse.json({ error: \"Failed to read uploaded file from file field\" }, { status: 500 })\n      }\n    }\n\n    // Check if CSV data was sent as text in csvData field\n    const csvData = formData.get('csvData') as string\n    if (csvData) {\n      console.log('Found CSV data in csvData field')\n      return await processCsvText(csvData)\n    }\n\n    console.log('No CSV data found in any field')\n    return NextResponse.json({ error: \"No CSV file or data provided\" }, { status: 400 })\n\n  } catch (error) {\n    console.error(\"Error processing CSV:\", error)\n    return NextResponse.json(\n      {\n        error: \"Failed to process CSV data\",\n        details: error instanceof Error ? error.message : \"Unknown error\",\n      },\n      { status: 500, headers },\n    )\n  }\n}\n\n// Helper function to process CSV text\nasync function processCsvText(csvText: string) {\n  try {\n    // Parse CSV\n    const lines = csvText.trim().split(\"\\n\")\n\n    if (lines.length === 0) {\n      return NextResponse.json({ error: \"Empty CSV data\" }, { status: 400 })\n    }\n\n    const headers = lines[0].split(\",\").map((h) => h.trim().replace(/\"/g, \"\"))\n\n    const cases = []\n    for (let i = 1; i < lines.length; i++) {\n      const values = lines[i].split(\",\").map((v) => v.trim().replace(/\"/g, \"\"))\n      if (values.length === headers.length) {\n        const caseData: Record<string, string> = {}\n        headers.forEach((header, index) => {\n          caseData[header] = values[index]\n        })\n\n        // Auto-detect and assign location based on CSV content\n        const detectedLocation = detectLocation(caseData, csvText)\n        caseData.taluka = detectedLocation\n\n        cases.push(caseData)\n      }\n    }\n\n    // Categorize cases by location\n    const igatpuriCases = cases.filter(c => c.taluka === \"Igatpuri\")\n    const trimbakeshwarCases = cases.filter(c => c.taluka === \"Trimbakeshwar\")\n\n    // Save to localStorage for each location\n    await saveCasesToStorage(igatpuriCases, trimbakeshwarCases)\n\n    console.log(`Processed ${cases.length} cases from CSV - Igatpuri: ${igatpuriCases.length}, Trimbakeshwar: ${trimbakeshwarCases.length}`)\n\n    // Initialize database and save cases\n    try {\n      await initializeDatabase()\n\n      // Convert cases to database format with new structure\n      const dbCases = cases.map(case_ => ({\n        sr_no: case_[\"Sr No\"] || \"\",\n        case_number: case_[\"Case Number\"] || \"\",\n        case_type: case_[\"Case Type\"] || \"\",\n        applicant_name: case_[\"Appellant\"] || case_[\"Applicant Name\"] || \"\",\n        respondent_name: case_[\"Respondent\"] || case_[\"Respondent Name\"] || \"\",\n        received: case_[\"Received\"] || \"\",\n        next_date: case_[\"Next Date\"] || \"\",\n        status: case_[\"Status\"] || \"\",\n        remarks: case_[\"Remarks\"] || \"\",\n        taluka: case_.taluka || \"Unknown\"\n      }))\n\n      const result = await upsertCases(dbCases)\n      if (!result.success) {\n        console.error('Error saving cases to database:', result.error)\n      } else {\n        console.log('Cases saved to database successfully')\n      }\n    } catch (dbError) {\n      console.error('Error with database operation:', dbError)\n      // Continue anyway - don't fail the API call\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: `Successfully processed ${cases.length} cases`,\n      breakdown: {\n        igatpuri: igatpuriCases.length,\n        trimbakeshwar: trimbakeshwarCases.length\n      },\n      detectedLocation: cases.length > 0 ? cases[0].taluka : \"Unknown\",\n      cases: cases.slice(0, 5), // Return first 5 for verification\n      totalCases: cases.length,\n      headers: headers\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'POST, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type',\n      }\n    })\n  } catch (error) {\n    console.error(\"Error parsing CSV:\", error)\n    return NextResponse.json(\n      {\n        error: \"Failed to parse CSV data\",\n        details: error instanceof Error ? error.message : \"Unknown error\",\n      },\n      {\n        status: 500,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        }\n      },\n    )\n  }\n}\n\n// Helper function to detect location based on CSV content\nfunction detectLocation(caseData: Record<string, string>, csvText: string): string {\n  // Method 1: Check case numbers or IDs for patterns (most specific)\n  const caseNumber = caseData['Case Number'] || caseData['caseNumber'] || caseData['Case ID'] || caseData['caseId']\n  if (caseNumber) {\n    const caseNum = caseNumber.toLowerCase()\n    if (caseNum.includes('tmb') || caseNum.includes('trimbakeshwar')) return \"Trimbakeshwar\"\n    if (caseNum.includes('igt') || caseNum.includes('igatpuri')) return \"Igatpuri\"\n  }\n\n  // Method 2: Check each field individually for location info (case-by-case basis)\n  for (const [key, value] of Object.entries(caseData)) {\n    const val = value?.toLowerCase() || ''\n    // Check for Devanagari/Marathi text\n    if (val.includes('त्र्यंबकेश्वर') || val.includes('trimbakeshwar')) return \"Trimbakeshwar\"\n    if (val.includes('इगतपुरी') || val.includes('igatpuri')) return \"Igatpuri\"\n  }\n\n  // Method 3: Check if there's already a taluka/location field\n  const locationFields = ['taluka', 'location', 'court', 'office', 'jurisdiction']\n  for (const field of locationFields) {\n    const value = caseData[field]?.toLowerCase()\n    if (value) {\n      if (value.includes('त्र्यंबकेश्वर') || value.includes('trimbakeshwar')) return \"Trimbakeshwar\"\n      if (value.includes('इगतपुरी') || value.includes('igatpuri')) return \"Igatpuri\"\n    }\n  }\n\n  // Method 4: Check filename or overall CSV content (last resort)\n  const textToCheck = csvText.toLowerCase()\n  if ((textToCheck.includes('त्र्यंबकेश्वर') || textToCheck.includes('trimbakeshwar')) &&\n      !(textToCheck.includes('इगतपुरी') || textToCheck.includes('igatpuri'))) return \"Trimbakeshwar\"\n  if ((textToCheck.includes('इगतपुरी') || textToCheck.includes('igatpuri')) &&\n      !(textToCheck.includes('त्र्यंबकेश्वर') || textToCheck.includes('trimbakeshwar'))) return \"Igatpuri\"\n\n  // Default: If no location detected, return Igatpuri as default\n  return \"Igatpuri\" // Default location\n}\n\n// Helper function to save cases to storage (simulating database)\nasync function saveCasesToStorage(igatpuriCases: any[], trimbakeshwarCases: any[]) {\n  // In a real application, this would save to a database\n  // For now, we'll just log the categorization\n  console.log(`Saving cases - Igatpuri: ${igatpuriCases.length}, Trimbakeshwar: ${trimbakeshwarCases.length}`)\n\n  // You could implement actual storage logic here\n  // For example, saving to different database tables or files\n}\n\n// Handle CORS preflight requests\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n\n// GET endpoint to fetch current cases\nexport async function GET() {\n  try {\n    await initializeDatabase()\n\n    const cases = await getAllCases()\n    const stats = await getCaseStats()\n\n    // Convert database format back to dashboard format (CaseData interface)\n    const dashboardCases = cases.map(case_ => ({\n      date: case_.created_at || new Date().toISOString(),\n      caseType: case_.case_type || \"अपील\",\n      caseNumber: case_.case_number,\n      appellant: case_.applicant_name,\n      respondent: case_.respondent_name,\n      received: case_.received || \"प्राप्त\",\n      nextDate: case_.next_date || \"2025-07-17\",\n      status: case_.status || \"\",\n      taluka: case_.taluka,\n      filedDate: case_.created_at || new Date().toISOString(),\n      lastUpdate: case_.updated_at || case_.created_at || new Date().toISOString()\n    }))\n\n    const breakdown = {\n      igatpuri: stats.byTaluka.find(s => s.taluka === 'Igatpuri')?.total_cases || 0,\n      trimbakeshwar: stats.byTaluka.find(s => s.taluka === 'Trimbakeshwar')?.total_cases || 0\n    }\n\n    return NextResponse.json({\n      success: true,\n      cases: dashboardCases,\n      lastUpdated: cases.length > 0 ? cases[0].created_at : new Date().toISOString(),\n      breakdown: breakdown,\n      stats: stats\n    }, {\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type',\n      }\n    })\n  } catch (error) {\n    console.error('Error fetching cases from database:', error)\n    return NextResponse.json({\n      success: false,\n      error: \"Failed to fetch cases from database\",\n      cases: []\n    }, {\n      status: 500,\n      headers: {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type',\n      }\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,mBAAmB;IACnB,MAAM,UAAU;QACd,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;IAClC;IACA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,MAAM;QAC7C,QAAQ,GAAG,CAAC,gBAAgB,QAAQ,GAAG;QACvC,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEjD,qCAAqC;QACrC,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,QAAQ,GAAG,CAAC;QAEZ,kDAAkD;QAClD,QAAQ,GAAG,CAAC,mBAAmB,MAAM,IAAI,CAAC,SAAS,IAAI;QACvD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,SAAS,OAAO,GAAI;YAC7C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,OAAO,iBAAiB,OAAO,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE,OAAO,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAClK;QAEA,oEAAoE;QACpE,MAAM,YAAY,SAAS,MAAM,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC;QAChE,QAAQ,GAAG,CAAC,0BAA0B,UAAU,MAAM;QAEtD,qFAAqF;QACrF,MAAM,WAAW,SAAS,GAAG,CAAC;QAC9B,IAAI,YAAY,OAAO,aAAa,UAAU;YAC5C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,qBAAqB,SAAS,SAAS,CAAC,GAAG;YACvD,IAAI;gBACF,OAAO,MAAM,eAAe;YAC9B,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;oBAA8B,SAAS,SAAS,OAAO;gBAAC,GAAG;oBAAE,QAAQ;gBAAI;YAC7G;QACF;QAEA,mEAAmE;QACnE,MAAM,aAAa,SAAS,GAAG,CAAC;QAChC,IAAI,cAAc,OAAO,eAAe,UAAU;YAChD,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,qBAAqB,WAAW,SAAS,CAAC,GAAG;YACzD,IAAI;gBACF,OAAO,MAAM,eAAe;YAC9B,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;oBAA8B,SAAS,SAAS,OAAO;gBAAC,GAAG;oBAAE,QAAQ;gBAAI;YAC7G;QACF;QAEA,yCAAyC;QACzC,IAAI,YAAY,oBAAoB,MAAM;YACxC,QAAQ,GAAG,CAAC,4BAA4B,SAAS,IAAI,EAAE,SAAS,SAAS,IAAI;YAC7E,IAAI;gBACF,MAAM,QAAQ,MAAM,SAAS,WAAW;gBACxC,MAAM,UAAU,IAAI,YAAY,SAAS,MAAM,CAAC;gBAChD,OAAO,MAAM,eAAe;YAC9B,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA8C,GAAG;oBAAE,QAAQ;gBAAI;YACnG;QACF;QAEA,kDAAkD;QAClD,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,IAAI,QAAQ,gBAAgB,MAAM;YAChC,QAAQ,GAAG,CAAC,6BAA6B,KAAK,IAAI,EAAE,SAAS,KAAK,IAAI;YACtE,IAAI;gBACF,MAAM,QAAQ,MAAM,KAAK,WAAW;gBACpC,MAAM,UAAU,IAAI,YAAY,SAAS,MAAM,CAAC;gBAChD,OAAO,MAAM,eAAe;YAC9B,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA+C,GAAG;oBAAE,QAAQ;gBAAI;YACpG;QACF;QAEA,sDAAsD;QACtD,MAAM,UAAU,SAAS,GAAG,CAAC;QAC7B,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC;YACZ,OAAO,MAAM,eAAe;QAC9B;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA+B,GAAG;YAAE,QAAQ;QAAI;IAEpF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;YAAK;QAAQ;IAE3B;AACF;AAEA,sCAAsC;AACtC,eAAe,eAAe,OAAe;IAC3C,IAAI;QACF,YAAY;QACZ,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QAEnC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM;QAEtE,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM;YACrE,IAAI,OAAO,MAAM,KAAK,QAAQ,MAAM,EAAE;gBACpC,MAAM,WAAmC,CAAC;gBAC1C,QAAQ,OAAO,CAAC,CAAC,QAAQ;oBACvB,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM;gBAClC;gBAEA,uDAAuD;gBACvD,MAAM,mBAAmB,eAAe,UAAU;gBAClD,SAAS,MAAM,GAAG;gBAElB,MAAM,IAAI,CAAC;YACb;QACF;QAEA,+BAA+B;QAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACrD,MAAM,qBAAqB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAE1D,yCAAyC;QACzC,MAAM,mBAAmB,eAAe;QAExC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,4BAA4B,EAAE,cAAc,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,MAAM,EAAE;QAEvI,qCAAqC;QACrC,IAAI;YACF,MAAM,CAAA,GAAA,2GAAA,CAAA,qBAAkB,AAAD;YAEvB,sDAAsD;YACtD,MAAM,UAAU,MAAM,GAAG,CAAC,CAAA,QAAS,CAAC;oBAClC,OAAO,KAAK,CAAC,QAAQ,IAAI;oBACzB,aAAa,KAAK,CAAC,cAAc,IAAI;oBACrC,WAAW,KAAK,CAAC,YAAY,IAAI;oBACjC,gBAAgB,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,iBAAiB,IAAI;oBACjE,iBAAiB,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,kBAAkB,IAAI;oBACpE,UAAU,KAAK,CAAC,WAAW,IAAI;oBAC/B,WAAW,KAAK,CAAC,YAAY,IAAI;oBACjC,QAAQ,KAAK,CAAC,SAAS,IAAI;oBAC3B,SAAS,KAAK,CAAC,UAAU,IAAI;oBAC7B,QAAQ,MAAM,MAAM,IAAI;gBAC1B,CAAC;YAED,MAAM,SAAS,MAAM,CAAA,GAAA,2GAAA,CAAA,cAAW,AAAD,EAAE;YACjC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,QAAQ,KAAK,CAAC,mCAAmC,OAAO,KAAK;YAC/D,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,4CAA4C;QAC9C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,CAAC,uBAAuB,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;YACvD,WAAW;gBACT,UAAU,cAAc,MAAM;gBAC9B,eAAe,mBAAmB,MAAM;YAC1C;YACA,kBAAkB,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;YACvD,OAAO,MAAM,KAAK,CAAC,GAAG;YACtB,YAAY,MAAM,MAAM;YACxB,SAAS;QACX,GAAG;YACD,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YACE,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IAEJ;AACF;AAEA,0DAA0D;AAC1D,SAAS,eAAe,QAAgC,EAAE,OAAe;IACvE,mEAAmE;IACnE,MAAM,aAAa,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS;IACjH,IAAI,YAAY;QACd,MAAM,UAAU,WAAW,WAAW;QACtC,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,kBAAkB,OAAO;QACzE,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,aAAa,OAAO;IACtE;IAEA,iFAAiF;IACjF,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;QACnD,MAAM,MAAM,OAAO,iBAAiB;QACpC,oCAAoC;QACpC,IAAI,IAAI,QAAQ,CAAC,oBAAoB,IAAI,QAAQ,CAAC,kBAAkB,OAAO;QAC3E,IAAI,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,aAAa,OAAO;IAClE;IAEA,6DAA6D;IAC7D,MAAM,iBAAiB;QAAC;QAAU;QAAY;QAAS;QAAU;KAAe;IAChF,KAAK,MAAM,SAAS,eAAgB;QAClC,MAAM,QAAQ,QAAQ,CAAC,MAAM,EAAE;QAC/B,IAAI,OAAO;YACT,IAAI,MAAM,QAAQ,CAAC,oBAAoB,MAAM,QAAQ,CAAC,kBAAkB,OAAO;YAC/E,IAAI,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,aAAa,OAAO;QACtE;IACF;IAEA,gEAAgE;IAChE,MAAM,cAAc,QAAQ,WAAW;IACvC,IAAI,CAAC,YAAY,QAAQ,CAAC,oBAAoB,YAAY,QAAQ,CAAC,gBAAgB,KAC/E,CAAC,CAAC,YAAY,QAAQ,CAAC,cAAc,YAAY,QAAQ,CAAC,WAAW,GAAG,OAAO;IACnF,IAAI,CAAC,YAAY,QAAQ,CAAC,cAAc,YAAY,QAAQ,CAAC,WAAW,KACpE,CAAC,CAAC,YAAY,QAAQ,CAAC,oBAAoB,YAAY,QAAQ,CAAC,gBAAgB,GAAG,OAAO;IAE9F,+DAA+D;IAC/D,OAAO,WAAW,mBAAmB;;AACvC;AAEA,iEAAiE;AACjE,eAAe,mBAAmB,aAAoB,EAAE,kBAAyB;IAC/E,uDAAuD;IACvD,6CAA6C;IAC7C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,cAAc,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,MAAM,EAAE;AAE3G,gDAAgD;AAChD,4DAA4D;AAC9D;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,2GAAA,CAAA,qBAAkB,AAAD;QAEvB,MAAM,QAAQ,MAAM,CAAA,GAAA,2GAAA,CAAA,cAAW,AAAD;QAC9B,MAAM,QAAQ,MAAM,CAAA,GAAA,2GAAA,CAAA,eAAY,AAAD;QAE/B,wEAAwE;QACxE,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,QAAS,CAAC;gBACzC,MAAM,MAAM,UAAU,IAAI,IAAI,OAAO,WAAW;gBAChD,UAAU,MAAM,SAAS,IAAI;gBAC7B,YAAY,MAAM,WAAW;gBAC7B,WAAW,MAAM,cAAc;gBAC/B,YAAY,MAAM,eAAe;gBACjC,UAAU,MAAM,QAAQ,IAAI;gBAC5B,UAAU,MAAM,SAAS,IAAI;gBAC7B,QAAQ,MAAM,MAAM,IAAI;gBACxB,QAAQ,MAAM,MAAM;gBACpB,WAAW,MAAM,UAAU,IAAI,IAAI,OAAO,WAAW;gBACrD,YAAY,MAAM,UAAU,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,WAAW;YAC5E,CAAC;QAED,MAAM,YAAY;YAChB,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,eAAe;YAC5E,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,kBAAkB,eAAe;QACxF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,aAAa,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,OAAO,WAAW;YAC5E,WAAW;YACX,OAAO;QACT,GAAG;YACD,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,OAAO,EAAE;QACX,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF;AACF", "debugId": null}}]}