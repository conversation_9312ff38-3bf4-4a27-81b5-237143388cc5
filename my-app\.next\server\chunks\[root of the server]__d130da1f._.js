module.exports = {

"[project]/.next-internal/server/app/api/cases/[caseNumber]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getAllCases": (()=>getAllCases),
    "getCaseByNumber": (()=>getCaseByNumber),
    "getCaseStats": (()=>getCaseStats),
    "getCasesByTaluka": (()=>getCasesByTaluka),
    "initializeDatabase": (()=>initializeDatabase),
    "updateCaseField": (()=>updateCaseField),
    "upsertCases": (()=>upsertCases)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$neondatabase$2f$serverless$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@neondatabase/serverless/index.mjs [app-route] (ecmascript)");
;
// Initialize Neon client
const sql = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$neondatabase$2f$serverless$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["neon"])(process.env.DATABASE_URL);
async function initializeDatabase() {
    try {
        await sql`
      CREATE TABLE IF NOT EXISTS legal_cases (
        id SERIAL PRIMARY KEY,
        sr_no VARCHAR(50),
        case_number VARCHAR(100) NOT NULL,
        case_type VARCHAR(100),
        applicant_name VARCHAR(255) NOT NULL,
        respondent_name VARCHAR(255) NOT NULL,
        received VARCHAR(100),
        next_date DATE,
        status TEXT,
        remarks TEXT,
        taluka VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
        // Add missing columns if they don't exist
        try {
            await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS case_type VARCHAR(100)`;
            await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS next_date DATE`;
            // Change status to TEXT to allow longer status messages
            await sql`ALTER TABLE legal_cases ALTER COLUMN status TYPE TEXT`;
        } catch (alterError) {
            console.log('Some columns may already exist:', alterError.message);
        }
        // Create index for better performance
        await sql`
      CREATE INDEX IF NOT EXISTS idx_legal_cases_taluka ON legal_cases(taluka)
    `;
        await sql`
      CREATE INDEX IF NOT EXISTS idx_legal_cases_case_number ON legal_cases(case_number)
    `;
        console.log('Database initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing database:', error);
        return false;
    }
}
async function upsertCases(cases) {
    try {
        // Clear existing cases (for now - in production you might want to do incremental updates)
        await sql`DELETE FROM legal_cases`;
        // Insert new cases
        for (const case_ of cases){
            await sql`
        INSERT INTO legal_cases (
          sr_no, case_number, case_type, applicant_name, respondent_name,
          received, next_date, status, remarks, taluka
        ) VALUES (
          ${case_.sr_no}, ${case_.case_number}, ${case_.case_type || 'अपील'},
          ${case_.applicant_name}, ${case_.respondent_name},
          ${case_.received || 'प्राप्त'}, ${case_.next_date || '2025-07-17'},
          ${case_.status}, ${case_.remarks}, ${case_.taluka}
        )
      `;
        }
        console.log(`Successfully inserted ${cases.length} cases`);
        return {
            success: true,
            count: cases.length
        };
    } catch (error) {
        console.error('Error upserting cases:', error);
        return {
            success: false,
            error: error.message
        };
    }
}
async function getAllCases() {
    try {
        const cases = await sql`
      SELECT * FROM legal_cases
      ORDER BY created_at DESC
    `;
        return cases;
    } catch (error) {
        console.error('Error fetching cases:', error);
        return [];
    }
}
async function getCasesByTaluka(taluka) {
    try {
        const cases = await sql`
      SELECT * FROM legal_cases
      WHERE taluka = ${taluka}
      ORDER BY created_at DESC
    `;
        return cases;
    } catch (error) {
        console.error('Error fetching cases by taluka:', error);
        return [];
    }
}
async function getCaseStats() {
    try {
        const stats = await sql`
      SELECT
        taluka,
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,
        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases
      FROM legal_cases
      GROUP BY taluka
    `;
        const totalStats = await sql`
      SELECT
        COUNT(*) as total_cases,
        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,
        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases
      FROM legal_cases
    `;
        return {
            byTaluka: stats,
            total: totalStats[0]
        };
    } catch (error) {
        console.error('Error fetching case stats:', error);
        return {
            byTaluka: [],
            total: {
                total_cases: 0,
                received_cases: 0,
                pending_cases: 0
            }
        };
    }
}
async function updateCaseField(caseNumber, field, value) {
    try {
        let query;
        switch(field){
            case 'status':
                query = sql`
          UPDATE legal_cases
          SET status = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            case 'received':
                query = sql`
          UPDATE legal_cases
          SET received = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            case 'next_date':
                query = sql`
          UPDATE legal_cases
          SET next_date = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            case 'case_type':
                query = sql`
          UPDATE legal_cases
          SET case_type = ${value}, updated_at = CURRENT_TIMESTAMP
          WHERE case_number = ${caseNumber}
        `;
                break;
            default:
                throw new Error(`Invalid field: ${field}`);
        }
        const result = await query;
        console.log(`Updated case ${caseNumber} field ${field} to ${value}`);
        return {
            success: true,
            updated: result.count || 0
        };
    } catch (error) {
        console.error(`Error updating case ${caseNumber} field ${field}:`, error);
        return {
            success: false,
            error: error.message
        };
    }
}
async function getCaseByNumber(caseNumber) {
    try {
        const cases = await sql`
      SELECT * FROM legal_cases
      WHERE case_number = ${caseNumber}
      LIMIT 1
    `;
        return cases.length > 0 ? cases[0] : null;
    } catch (error) {
        console.error('Error fetching case by number:', error);
        return null;
    }
}
}}),
"[project]/app/api/cases/[caseNumber]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS),
    "PATCH": (()=>PATCH)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/db.ts [app-route] (ecmascript)");
;
;
async function PATCH(request, { params }) {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'PATCH, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    };
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeDatabase"])();
        const { caseNumber } = await params;
        const body = await request.json();
        const { field, value } = body;
        if (!field || value === undefined) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Field and value are required"
            }, {
                status: 400,
                headers
            });
        }
        // Validate field
        const allowedFields = [
            'status',
            'received',
            'next_date',
            'case_type'
        ];
        if (!allowedFields.includes(field)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Invalid field. Allowed fields: ${allowedFields.join(', ')}`
            }, {
                status: 400,
                headers
            });
        }
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateCaseField"])(caseNumber, field, value);
        if (!result.success) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: result.error
            }, {
                status: 500,
                headers
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: `Updated ${field} for case ${caseNumber}`,
            updated: result.updated
        }, {
            headers
        });
    } catch (error) {
        console.error('Error updating case:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to update case"
        }, {
            status: 500,
            headers
        });
    }
}
async function GET(request, { params }) {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'PATCH, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    };
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeDatabase"])();
        const { caseNumber } = await params;
        const case_ = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCaseByNumber"])(caseNumber);
        if (!case_) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Case not found"
            }, {
                status: 404,
                headers
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            case: case_
        }, {
            headers
        });
    } catch (error) {
        console.error('Error fetching case:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to fetch case"
        }, {
            status: 500,
            headers
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'PATCH, GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__d130da1f._.js.map