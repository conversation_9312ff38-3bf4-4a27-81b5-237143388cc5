{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/lib/db.ts"], "sourcesContent": ["import { neon } from '@neondatabase/serverless'\n\n// Initialize Neon client\nconst sql = neon(process.env.DATABASE_URL!)\n\nexport interface CaseRecord {\n  id?: number\n  sr_no: string\n  case_number: string\n  case_type?: string\n  applicant_name: string\n  respondent_name: string\n  received?: string\n  next_date?: string\n  status: string\n  remarks: string\n  taluka: string\n  created_at?: string\n  updated_at?: string\n}\n\n// Create tables if they don't exist\nexport async function initializeDatabase() {\n  try {\n    await sql`\n      CREATE TABLE IF NOT EXISTS legal_cases (\n        id SERIAL PRIMARY KEY,\n        sr_no VARCHAR(50),\n        case_number VARCHAR(100) NOT NULL,\n        case_type VARCHAR(100),\n        applicant_name VARCHAR(255) NOT NULL,\n        respondent_name VARCHAR(255) NOT NULL,\n        received VARCHAR(100),\n        next_date DATE,\n        status TEXT,\n        remarks TEXT,\n        taluka VARCHAR(50) NOT NULL,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `\n\n    // Add missing columns if they don't exist\n    try {\n      await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS case_type VARCHAR(100)`\n      await sql`ALTER TABLE legal_cases ADD COLUMN IF NOT EXISTS next_date DATE`\n      // Change status to TEXT to allow longer status messages\n      await sql`ALTER TABLE legal_cases ALTER COLUMN status TYPE TEXT`\n    } catch (alterError) {\n      console.log('Some columns may already exist:', alterError.message)\n    }\n\n    // Create index for better performance\n    await sql`\n      CREATE INDEX IF NOT EXISTS idx_legal_cases_taluka ON legal_cases(taluka)\n    `\n\n    await sql`\n      CREATE INDEX IF NOT EXISTS idx_legal_cases_case_number ON legal_cases(case_number)\n    `\n\n    console.log('Database initialized successfully')\n    return true\n  } catch (error) {\n    console.error('Error initializing database:', error)\n    return false\n  }\n}\n\n// Insert or update cases\nexport async function upsertCases(cases: CaseRecord[]) {\n  try {\n    // Clear existing cases (for now - in production you might want to do incremental updates)\n    await sql`DELETE FROM legal_cases`\n\n    // Insert new cases\n    for (const case_ of cases) {\n      await sql`\n        INSERT INTO legal_cases (\n          sr_no, case_number, case_type, applicant_name, respondent_name,\n          received, next_date, status, remarks, taluka\n        ) VALUES (\n          ${case_.sr_no}, ${case_.case_number}, ${case_.case_type || 'अपील'},\n          ${case_.applicant_name}, ${case_.respondent_name},\n          ${case_.received || 'प्राप्त'}, ${case_.next_date || '2025-07-17'},\n          ${case_.status}, ${case_.remarks}, ${case_.taluka}\n        )\n      `\n    }\n\n    console.log(`Successfully inserted ${cases.length} cases`)\n    return { success: true, count: cases.length }\n  } catch (error) {\n    console.error('Error upserting cases:', error)\n    return { success: false, error: error.message }\n  }\n}\n\n// Get all cases\nexport async function getAllCases(): Promise<CaseRecord[]> {\n  try {\n    const cases = await sql`\n      SELECT * FROM legal_cases\n      ORDER BY created_at DESC\n    `\n    return cases as CaseRecord[]\n  } catch (error) {\n    console.error('Error fetching cases:', error)\n    return []\n  }\n}\n\n// Get cases by taluka\nexport async function getCasesByTaluka(taluka: string): Promise<CaseRecord[]> {\n  try {\n    const cases = await sql`\n      SELECT * FROM legal_cases\n      WHERE taluka = ${taluka}\n      ORDER BY created_at DESC\n    `\n    return cases as CaseRecord[]\n  } catch (error) {\n    console.error('Error fetching cases by taluka:', error)\n    return []\n  }\n}\n\n// Get case statistics\nexport async function getCaseStats() {\n  try {\n    const stats = await sql`\n      SELECT\n        taluka,\n        COUNT(*) as total_cases,\n        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,\n        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases\n      FROM legal_cases\n      GROUP BY taluka\n    `\n\n    const totalStats = await sql`\n      SELECT\n        COUNT(*) as total_cases,\n        COUNT(CASE WHEN status = 'प्राप्त' THEN 1 END) as received_cases,\n        COUNT(CASE WHEN status != 'प्राप्त' AND status != '----' THEN 1 END) as pending_cases\n      FROM legal_cases\n    `\n\n    return {\n      byTaluka: stats,\n      total: totalStats[0]\n    }\n  } catch (error) {\n    console.error('Error fetching case stats:', error)\n    return { byTaluka: [], total: { total_cases: 0, received_cases: 0, pending_cases: 0 } }\n  }\n}\n\n// Update individual case fields\nexport async function updateCaseField(caseNumber: string, field: string, value: string) {\n  try {\n    let query\n    switch (field) {\n      case 'status':\n        query = sql`\n          UPDATE legal_cases\n          SET status = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      case 'received':\n        query = sql`\n          UPDATE legal_cases\n          SET received = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      case 'next_date':\n        query = sql`\n          UPDATE legal_cases\n          SET next_date = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      case 'case_type':\n        query = sql`\n          UPDATE legal_cases\n          SET case_type = ${value}, updated_at = CURRENT_TIMESTAMP\n          WHERE case_number = ${caseNumber}\n        `\n        break\n      default:\n        throw new Error(`Invalid field: ${field}`)\n    }\n\n    const result = await query\n    console.log(`Updated case ${caseNumber} field ${field} to ${value}`)\n    return { success: true, updated: result.count || 0 }\n  } catch (error) {\n    console.error(`Error updating case ${caseNumber} field ${field}:`, error)\n    return { success: false, error: error.message }\n  }\n}\n\n// Get a specific case by case number\nexport async function getCaseByNumber(caseNumber: string): Promise<CaseRecord | null> {\n  try {\n    const cases = await sql`\n      SELECT * FROM legal_cases\n      WHERE case_number = ${caseNumber}\n      LIMIT 1\n    `\n    return cases.length > 0 ? cases[0] as CaseRecord : null\n  } catch (error) {\n    console.error('Error fetching case by number:', error)\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AAmBlC,eAAe;IACpB,IAAI;QACF,MAAM,GAAG,CAAC;;;;;;;;;;;;;;;;IAgBV,CAAC;QAED,0CAA0C;QAC1C,IAAI;YACF,MAAM,GAAG,CAAC,uEAAuE,CAAC;YAClF,MAAM,GAAG,CAAC,+DAA+D,CAAC;YAC1E,wDAAwD;YACxD,MAAM,GAAG,CAAC,qDAAqD,CAAC;QAClE,EAAE,OAAO,YAAY;YACnB,QAAQ,GAAG,CAAC,mCAAmC,WAAW,OAAO;QACnE;QAEA,sCAAsC;QACtC,MAAM,GAAG,CAAC;;IAEV,CAAC;QAED,MAAM,GAAG,CAAC;;IAEV,CAAC;QAED,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,eAAe,YAAY,KAAmB;IACnD,IAAI;QACF,0FAA0F;QAC1F,MAAM,GAAG,CAAC,uBAAuB,CAAC;QAElC,mBAAmB;QACnB,KAAK,MAAM,SAAS,MAAO;YACzB,MAAM,GAAG,CAAC;;;;;UAKN,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,SAAS,IAAI,OAAO;UAClE,EAAE,MAAM,cAAc,CAAC,EAAE,EAAE,MAAM,eAAe,CAAC;UACjD,EAAE,MAAM,QAAQ,IAAI,UAAU,EAAE,EAAE,MAAM,SAAS,IAAI,aAAa;UAClE,EAAE,MAAM,MAAM,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;;MAEtD,CAAC;QACH;QAEA,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;QACzD,OAAO;YAAE,SAAS;YAAM,OAAO,MAAM,MAAM;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;;IAGxB,CAAC;QACD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;AACF;AAGO,eAAe,iBAAiB,MAAc;IACnD,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;qBAEP,EAAE,OAAO;;IAE1B,CAAC;QACD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;;;;;;;IAQxB,CAAC;QAED,MAAM,aAAa,MAAM,GAAG,CAAC;;;;;;IAM7B,CAAC;QAED,OAAO;YACL,UAAU;YACV,OAAO,UAAU,CAAC,EAAE;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,UAAU,EAAE;YAAE,OAAO;gBAAE,aAAa;gBAAG,gBAAgB;gBAAG,eAAe;YAAE;QAAE;IACxF;AACF;AAGO,eAAe,gBAAgB,UAAkB,EAAE,KAAa,EAAE,KAAa;IACpF,IAAI;QACF,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,QAAQ,GAAG,CAAC;;uBAEG,EAAE,MAAM;8BACD,EAAE,WAAW;QACnC,CAAC;gBACD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;;yBAEK,EAAE,MAAM;8BACH,EAAE,WAAW;QACnC,CAAC;gBACD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;;0BAEM,EAAE,MAAM;8BACJ,EAAE,WAAW;QACnC,CAAC;gBACD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;;0BAEM,EAAE,MAAM;8BACJ,EAAE,WAAW;QACnC,CAAC;gBACD;YACF;gBACE,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,OAAO;QAC7C;QAEA,MAAM,SAAS,MAAM;QACrB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,WAAW,OAAO,EAAE,MAAM,IAAI,EAAE,OAAO;QACnE,OAAO;YAAE,SAAS;YAAM,SAAS,OAAO,KAAK,IAAI;QAAE;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,WAAW,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE;QACnE,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;AAGO,eAAe,gBAAgB,UAAkB;IACtD,IAAI;QACF,MAAM,QAAQ,MAAM,GAAG,CAAC;;0BAEF,EAAE,WAAW;;IAEnC,CAAC;QACD,OAAO,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,GAAiB;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/app/api/cases/%5BcaseNumber%5D/route.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from \"next/server\"\nimport { initializeDatabase, updateCaseField, getCaseByNumber } from \"@/lib/db\"\n\n// PATCH endpoint to update specific fields of a case\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ caseNumber: string }> }\n) {\n  const headers = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'PATCH, GET, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type',\n  }\n\n  try {\n    await initializeDatabase()\n\n    const { caseNumber } = await params\n    const body = await request.json()\n    const { field, value } = body\n\n    if (!field || value === undefined) {\n      return NextResponse.json(\n        { error: \"Field and value are required\" },\n        { status: 400, headers }\n      )\n    }\n\n    // Validate field\n    const allowedFields = ['status', 'received', 'next_date', 'case_type']\n    if (!allowedFields.includes(field)) {\n      return NextResponse.json(\n        { error: `Invalid field. Allowed fields: ${allowedFields.join(', ')}` },\n        { status: 400, headers }\n      )\n    }\n\n    const result = await updateCaseField(caseNumber, field, value)\n\n    if (!result.success) {\n      return NextResponse.json(\n        { error: result.error },\n        { status: 500, headers }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: `Updated ${field} for case ${caseNumber}`,\n      updated: result.updated\n    }, { headers })\n\n  } catch (error) {\n    console.error('Error updating case:', error)\n    return NextResponse.json(\n      { error: \"Failed to update case\" },\n      { status: 500, headers }\n    )\n  }\n}\n\n// GET endpoint to fetch a specific case\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ caseNumber: string }> }\n) {\n  const headers = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'PATCH, GET, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type',\n  }\n\n  try {\n    await initializeDatabase()\n\n    const { caseNumber } = await params\n    const case_ = await getCaseByNumber(caseNumber)\n\n    if (!case_) {\n      return NextResponse.json(\n        { error: \"Case not found\" },\n        { status: 404, headers }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      case: case_\n    }, { headers })\n\n  } catch (error) {\n    console.error('Error fetching case:', error)\n    return NextResponse.json(\n      { error: \"Failed to fetch case\" },\n      { status: 500, headers }\n    )\n  }\n}\n\n// Handle CORS preflight requests\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'PATCH, GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAA+C;IAEvD,MAAM,UAAU;QACd,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;IAClC;IAEA,IAAI;QACF,MAAM,CAAA,GAAA,2GAAA,CAAA,qBAAkB,AAAD;QAEvB,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;QAC7B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;QAEzB,IAAI,CAAC,SAAS,UAAU,WAAW;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;gBAAK;YAAQ;QAE3B;QAEA,iBAAiB;QACjB,MAAM,gBAAgB;YAAC;YAAU;YAAY;YAAa;SAAY;QACtE,IAAI,CAAC,cAAc,QAAQ,CAAC,QAAQ;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,+BAA+B,EAAE,cAAc,IAAI,CAAC,OAAO;YAAC,GACtE;gBAAE,QAAQ;gBAAK;YAAQ;QAE3B;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,2GAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,OAAO;QAExD,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,OAAO,KAAK;YAAC,GACtB;gBAAE,QAAQ;gBAAK;YAAQ;QAE3B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,CAAC,QAAQ,EAAE,MAAM,UAAU,EAAE,YAAY;YAClD,SAAS,OAAO,OAAO;QACzB,GAAG;YAAE;QAAQ;IAEf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;YAAK;QAAQ;IAE3B;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA+C;IAEvD,MAAM,UAAU;QACd,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;IAClC;IAEA,IAAI;QACF,MAAM,CAAA,GAAA,2GAAA,CAAA,qBAAkB,AAAD;QAEvB,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;QAC7B,MAAM,QAAQ,MAAM,CAAA,GAAA,2GAAA,CAAA,kBAAe,AAAD,EAAE;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;gBAAK;YAAQ;QAE3B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR,GAAG;YAAE;QAAQ;IAEf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;YAAK;QAAQ;IAE3B;AACF;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}