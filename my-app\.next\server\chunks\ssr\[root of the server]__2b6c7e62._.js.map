{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/lib/api.ts"], "sourcesContent": ["// API utility functions for case management\n\nexport interface UpdateCaseFieldRequest {\n  field: 'status' | 'received' | 'next_date' | 'case_type'\n  value: string\n}\n\nexport interface ApiResponse<T = any> {\n  success: boolean\n  message?: string\n  error?: string\n  data?: T\n}\n\n// Update a specific field of a case\nexport async function updateCaseField(\n  caseNumber: string, \n  field: UpdateCaseFieldRequest['field'], \n  value: string\n): Promise<ApiResponse> {\n  try {\n    const response = await fetch(`/api/cases/${encodeURIComponent(caseNumber)}`, {\n      method: 'PATCH',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ field, value }),\n    })\n\n    const data = await response.json()\n    \n    if (!response.ok) {\n      return {\n        success: false,\n        error: data.error || `HTTP ${response.status}`,\n      }\n    }\n\n    return {\n      success: true,\n      message: data.message,\n      data: data,\n    }\n  } catch (error) {\n    console.error('Error updating case field:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    }\n  }\n}\n\n// Get a specific case\nexport async function getCase(caseNumber: string): Promise<ApiResponse> {\n  try {\n    const response = await fetch(`/api/cases/${encodeURIComponent(caseNumber)}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    const data = await response.json()\n    \n    if (!response.ok) {\n      return {\n        success: false,\n        error: data.error || `HTTP ${response.status}`,\n      }\n    }\n\n    return {\n      success: true,\n      data: data.case,\n    }\n  } catch (error) {\n    console.error('Error fetching case:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    }\n  }\n}\n\n// Refresh all cases data\nexport async function refreshCasesData(): Promise<ApiResponse> {\n  try {\n    const response = await fetch('/api/update-cases', {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    const data = await response.json()\n    \n    if (!response.ok) {\n      return {\n        success: false,\n        error: data.error || `HTTP ${response.status}`,\n      }\n    }\n\n    return {\n      success: true,\n      data: data,\n    }\n  } catch (error) {\n    console.error('Error refreshing cases data:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;;AAerC,eAAe,gBACpB,UAAkB,EAClB,KAAsC,EACtC,KAAa;IAEb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,mBAAmB,aAAa,EAAE;YAC3E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAM;QACtC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAChD;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,KAAK,OAAO;YACrB,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,QAAQ,UAAkB;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,mBAAmB,aAAa,EAAE;YAC3E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAChD;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,KAAK,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAChD;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/lib/csv-utils.ts"], "sourcesContent": ["export interface CaseData {\n  date: string\n  caseType: string\n  caseNumber: string\n  appellant: string\n  respondent: string\n  received: string  // This will store the original \"प्राप्त\" or similar values\n  nextDate: string  // New field for next hearing date\n  status: string    // This will be the new editable status field\n  taluka: string\n  filedDate: string\n  lastUpdate: string\n}\n\nexport function parseCsvToCases(csvText: string): CaseData[] {\n  const lines = csvText.trim().split(\"\\n\")\n  if (lines.length < 2) return []\n\n  const headers = lines[0].split(\",\").map((h) => h.trim().toLowerCase())\n  const cases: CaseData[] = []\n\n  for (let i = 1; i < lines.length; i++) {\n    const values = lines[i].split(\",\").map((v) => v.trim())\n    if (values.length < headers.length) continue\n\n    const caseData: Partial<CaseData> = {}\n\n    headers.forEach((header, index) => {\n      const value = values[index] || \"\"\n\n      switch (header) {\n        case \"date\":\n          // Convert DD/MM/YYYY to YYYY-MM-DD\n          const dateParts = value.split(\"/\")\n          if (dateParts.length === 3) {\n            const [day, month, year] = dateParts\n            caseData.date = `${year}-${month.padStart(2, \"0\")}-${day.padStart(2, \"0\")}`\n          } else {\n            caseData.date = value\n          }\n          break\n        case \"case type\":\n        case \"casetype\":\n          caseData.caseType = value\n          break\n        case \"case number\":\n        case \"casenumber\":\n          caseData.caseNumber = value\n          break\n        case \"appellant\":\n          caseData.appellant = value\n          break\n        case \"respondent\":\n          caseData.respondent = value\n          break\n        case \"received\":\n          caseData.received = value\n          break\n        case \"next date\":\n        case \"nextdate\":\n          caseData.nextDate = value\n          break\n        case \"status\":\n        case \"custom_status\":\n        case \"custom status\":\n        case \"editable_status\":\n          caseData.status = value\n          break\n        case \"taluka\":\n          caseData.taluka = value\n          break\n      }\n    })\n\n    // Set default values and derive missing fields\n    if (caseData.date && caseData.caseType && caseData.caseNumber) {\n      const finalCase: CaseData = {\n        date: caseData.date,\n        caseType: caseData.caseType,\n        caseNumber: caseData.caseNumber,\n        appellant: caseData.appellant || \"Unknown\",\n        respondent: caseData.respondent || \"Unknown\",\n        received: caseData.received || \"प्राप्त\",  // Default to \"प्राप्त\" if no received status\n        nextDate: caseData.nextDate || \"2025-07-17\",  // Default to 17th July 2025\n        status: caseData.status || \"\",  // Default to empty for editable status\n        taluka: caseData.taluka || \"Unknown\",\n        filedDate: caseData.date,\n        lastUpdate: new Date().toISOString().split(\"T\")[0],\n      }\n\n      cases.push(finalCase)\n    }\n  }\n\n  return cases\n}\n\nexport function formatDateForDisplay(dateString: string): string {\n  if (!dateString) return \"N/A\"\n\n  try {\n    const date = new Date(dateString)\n    return date.toLocaleDateString(\"en-IN\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\",\n    })\n  } catch {\n    return dateString\n  }\n}\n\nexport function isValidDate(dateString: string): boolean {\n  if (!dateString) return false\n  const date = new Date(dateString)\n  return !isNaN(date.getTime())\n}\n"], "names": [], "mappings": ";;;;;AAcO,SAAS,gBAAgB,OAAe;IAC7C,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;IACnC,IAAI,MAAM,MAAM,GAAG,GAAG,OAAO,EAAE;IAE/B,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,WAAW;IACnE,MAAM,QAAoB,EAAE;IAE5B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;QACpD,IAAI,OAAO,MAAM,GAAG,QAAQ,MAAM,EAAE;QAEpC,MAAM,WAA8B,CAAC;QAErC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,MAAM,QAAQ,MAAM,CAAC,MAAM,IAAI;YAE/B,OAAQ;gBACN,KAAK;oBACH,mCAAmC;oBACnC,MAAM,YAAY,MAAM,KAAK,CAAC;oBAC9B,IAAI,UAAU,MAAM,KAAK,GAAG;wBAC1B,MAAM,CAAC,KAAK,OAAO,KAAK,GAAG;wBAC3B,SAAS,IAAI,GAAG,GAAG,KAAK,CAAC,EAAE,MAAM,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,QAAQ,CAAC,GAAG,MAAM;oBAC7E,OAAO;wBACL,SAAS,IAAI,GAAG;oBAClB;oBACA;gBACF,KAAK;gBACL,KAAK;oBACH,SAAS,QAAQ,GAAG;oBACpB;gBACF,KAAK;gBACL,KAAK;oBACH,SAAS,UAAU,GAAG;oBACtB;gBACF,KAAK;oBACH,SAAS,SAAS,GAAG;oBACrB;gBACF,KAAK;oBACH,SAAS,UAAU,GAAG;oBACtB;gBACF,KAAK;oBACH,SAAS,QAAQ,GAAG;oBACpB;gBACF,KAAK;gBACL,KAAK;oBACH,SAAS,QAAQ,GAAG;oBACpB;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,SAAS,MAAM,GAAG;oBAClB;gBACF,KAAK;oBACH,SAAS,MAAM,GAAG;oBAClB;YACJ;QACF;QAEA,+CAA+C;QAC/C,IAAI,SAAS,IAAI,IAAI,SAAS,QAAQ,IAAI,SAAS,UAAU,EAAE;YAC7D,MAAM,YAAsB;gBAC1B,MAAM,SAAS,IAAI;gBACnB,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,WAAW,SAAS,SAAS,IAAI;gBACjC,YAAY,SAAS,UAAU,IAAI;gBACnC,UAAU,SAAS,QAAQ,IAAI;gBAC/B,UAAU,SAAS,QAAQ,IAAI;gBAC/B,QAAQ,SAAS,MAAM,IAAI;gBAC3B,QAAQ,SAAS,MAAM,IAAI;gBAC3B,WAAW,SAAS,IAAI;gBACxB,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD;YAEA,MAAM,IAAI,CAAC;QACb;IACF;IAEA,OAAO;AACT;AAEO,SAAS,qBAAqB,UAAkB;IACrD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,YAAY,UAAkB;IAC5C,IAAI,CAAC,YAAY,OAAO;IACxB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,CAAC,MAAM,KAAK,OAAO;AAC5B", "debugId": null}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/hooks/use-cases.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { type CaseData, parseCsvToCases } from \"@/lib/csv-utils\"\n\n// Sample data with new structure\nconst sampleCases: CaseData[] = [\n  {\n    date: \"2024-01-15\",\n    caseType: \"अपील\",\n    caseNumber: \"अपील/150/2023\",\n    appellant: \"लक्ष्मीबाई शेलार\",\n    respondent: \"सुनीता शेलार\",\n    received: \"प्राप्त\",\n    nextDate: \"2025-07-17\",\n    status: \"\",\n    taluka: \"Igatpuri\",\n    filedDate: \"2024-01-15\",\n    lastUpdate: \"2024-01-15\"\n  },\n  {\n    date: \"2024-01-16\",\n    caseType: \"रिव्हीजन\",\n    caseNumber: \"रिव्हीजन/139/2023\",\n    appellant: \"चंद्रबाई हंबीर\",\n    respondent: \"गंगुबाई आघाण\",\n    received: \"प्राप्त\",\n    nextDate: \"2025-07-17\",\n    status: \"\",\n    taluka: \"Igatpuri\",\n    filedDate: \"2024-01-16\",\n    lastUpdate: \"2024-01-16\"\n  },\n  {\n    date: \"2024-01-17\",\n    caseType: \"मामलेदार कोर्ट\",\n    caseNumber: \"मामलेदार/131/2023\",\n    appellant: \"अरुण पोरजे\",\n    respondent: \"मनोज चौधरी\",\n    received: \"प्राप्त\",\n    nextDate: \"2025-07-17\",\n    status: \"\",\n    taluka: \"Trimbakeshwar\",\n    filedDate: \"2024-01-17\",\n    lastUpdate: \"2024-01-17\"\n  },\n  {\n    date: \"2024-01-18\",\n    caseType: \"गौणखनिज\",\n    caseNumber: \"गौणखनिज/113/2023\",\n    appellant: \"केरुजी काळे\",\n    respondent: \"कोंडाजी भालेराव\",\n    received: \"प्राप्त\",\n    nextDate: \"2025-07-17\",\n    status: \"\",\n    taluka: \"Trimbakeshwar\",\n    filedDate: \"2024-01-18\",\n    lastUpdate: \"2024-01-18\"\n  },\n  {\n    date: \"2024-01-19\",\n    caseType: \"अतिक्रमण\",\n    caseNumber: \"अतिक्रमण/104/2023\",\n    appellant: \"रामभाऊ ढोन्नर\",\n    respondent: \"अंबाबाई ढोन्नर उर्फ बिन्नर\",\n    received: \"प्राप्त\",\n    nextDate: \"2025-07-17\",\n    status: \"\",\n    taluka: \"Igatpuri\",\n    filedDate: \"2024-01-19\",\n    lastUpdate: \"2024-01-19\"\n  },\n  {\n    date: \"2024-01-20\",\n    caseType: \"कुळ कायदा\",\n    caseNumber: \"कुळ/90/2023\",\n    appellant: \"अनुसया मालुंजकर\",\n    respondent: \"ओम मालुंजकर\",\n    received: \"प्राप्त\",\n    nextDate: \"2025-07-17\",\n    status: \"\",\n    taluka: \"Trimbakeshwar\",\n    filedDate: \"2024-01-20\",\n    lastUpdate: \"2024-01-20\"\n  }\n]\n\nexport function useCases() {\n  const [cases, setCases] = useState<CaseData[]>(sampleCases)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [lastUpdated, setLastUpdated] = useState<Date | null>(new Date())\n\n  // Load initial data\n  useEffect(() => {\n    loadCases()\n  }, [])\n\n  // Load from localStorage on component mount\n  useEffect(() => {\n    const savedCases = localStorage.getItem(\"legal-cases\")\n    if (savedCases) {\n      try {\n        const parsed = JSON.parse(savedCases)\n        if (parsed.cases && Array.isArray(parsed.cases)) {\n          setCases(parsed.cases)\n          setLastUpdated(new Date(parsed.lastUpdated))\n          console.log(`Loaded ${parsed.cases.length} cases from localStorage`)\n        }\n      } catch (error) {\n        console.error(\"Error parsing saved cases:\", error)\n      }\n    }\n  }, [])\n\n  const loadCases = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      // First try to fetch from API\n      try {\n        console.log('Fetching cases from API...')\n        const response = await fetch('/api/update-cases')\n        console.log('API response status:', response.status)\n\n        if (response.ok) {\n          const data = await response.json()\n          console.log('API response data:', data)\n\n          if (data.success && data.cases) {\n            console.log(`Successfully loaded ${data.cases.length} cases from API`)\n            setCases(data.cases)\n            setLastUpdated(data.lastUpdated ? new Date(data.lastUpdated) : new Date())\n\n            // Also save to localStorage for offline access\n            localStorage.setItem(\"legal-cases\", JSON.stringify({\n              cases: data.cases,\n              lastUpdated: data.lastUpdated || new Date().toISOString(),\n            }))\n            return\n          } else {\n            console.log('API response missing success or cases:', data)\n          }\n        } else {\n          console.log('API response not ok:', response.status, response.statusText)\n        }\n      } catch (apiError) {\n        console.log('API not available, falling back to localStorage:', apiError)\n      }\n\n      // Fallback to localStorage if API fails\n      const savedCases = localStorage.getItem(\"legal-cases\")\n      if (savedCases) {\n        const parsed = JSON.parse(savedCases)\n        setCases(parsed.cases || sampleCases)\n        setLastUpdated(new Date(parsed.lastUpdated))\n      } else {\n        setCases(sampleCases)\n        setLastUpdated(new Date())\n      }\n    } catch (err) {\n      setError(\"Failed to load cases\")\n      console.error(\"Error loading cases:\", err)\n      setCases(sampleCases) // Fallback to sample data\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateCasesFromCsv = async (csvUrl: string) => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      // Fetch CSV data\n      const response = await fetch(csvUrl)\n      if (!response.ok) {\n        throw new Error(`Failed to fetch CSV: ${response.status}`)\n      }\n\n      const csvText = await response.text()\n      const newCases = parseCsvToCases(csvText)\n\n      // Update state\n      setCases(newCases.length > 0 ? newCases : sampleCases)\n      setLastUpdated(new Date())\n\n      // Save to localStorage for persistence\n      localStorage.setItem(\n        \"legal-cases\",\n        JSON.stringify({\n          cases: newCases.length > 0 ? newCases : sampleCases,\n          lastUpdated: new Date().toISOString(),\n        }),\n      )\n\n      console.log(`Successfully updated ${newCases.length} cases from CSV`)\n      return { success: true, count: newCases.length }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update cases\"\n      setError(errorMessage)\n      console.error(\"Error updating cases from CSV:\", err)\n      return { success: false, error: errorMessage }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const refreshCases = () => {\n    // Clear localStorage to force API fetch\n    localStorage.removeItem(\"legal-cases\")\n    loadCases()\n  }\n\n  const addCase = (newCase: CaseData) => {\n    try {\n      // Add the new case to the existing cases\n      const updatedCases = [...cases, newCase]\n      setCases(updatedCases)\n      setLastUpdated(new Date())\n\n      // Save to localStorage for persistence\n      localStorage.setItem(\n        \"legal-cases\",\n        JSON.stringify({\n          cases: updatedCases,\n          lastUpdated: new Date().toISOString(),\n        }),\n      )\n\n      console.log(`Successfully added new case: ${newCase.caseNumber}`)\n      return { success: true }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to add case\"\n      setError(errorMessage)\n      console.error(\"Error adding case:\", err)\n      return { success: false, error: errorMessage }\n    }\n  }\n\n  return {\n    cases,\n    loading,\n    error,\n    lastUpdated,\n    updateCasesFromCsv,\n    refreshCases,\n    addCase,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKA,iCAAiC;AACjC,MAAM,cAA0B;IAC9B;QACE,MAAM;QACN,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,YAAY;IACd;IACA;QACE,MAAM;QACN,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,YAAY;IACd;IACA;QACE,MAAM;QACN,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,YAAY;IACd;IACA;QACE,MAAM;QACN,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,YAAY;IACd;IACA;QACE,MAAM;QACN,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,YAAY;IACd;IACA;QACE,MAAM;QACN,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,YAAY;IACd;CACD;AAEM,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEhE,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,OAAO,KAAK,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG;oBAC/C,SAAS,OAAO,KAAK;oBACrB,eAAe,IAAI,KAAK,OAAO,WAAW;oBAC1C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC;gBACrE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YAET,8BAA8B;YAC9B,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,MAAM,MAAM;gBAC7B,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;gBAEnD,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,QAAQ,GAAG,CAAC,sBAAsB;oBAElC,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;wBAC9B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC;wBACrE,SAAS,KAAK,KAAK;wBACnB,eAAe,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI,IAAI;wBAEnE,+CAA+C;wBAC/C,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;4BACjD,OAAO,KAAK,KAAK;4BACjB,aAAa,KAAK,WAAW,IAAI,IAAI,OAAO,WAAW;wBACzD;wBACA;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC,0CAA0C;oBACxD;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM,EAAE,SAAS,UAAU;gBAC1E;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,GAAG,CAAC,oDAAoD;YAClE;YAEA,wCAAwC;YACxC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,SAAS,OAAO,KAAK,IAAI;gBACzB,eAAe,IAAI,KAAK,OAAO,WAAW;YAC5C,OAAO;gBACL,SAAS;gBACT,eAAe,IAAI;YACrB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,aAAa,0BAA0B;;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,SAAS;YAET,iBAAiB;YACjB,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,EAAE;YAC3D;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,MAAM,WAAW,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;YAEjC,eAAe;YACf,SAAS,SAAS,MAAM,GAAG,IAAI,WAAW;YAC1C,eAAe,IAAI;YAEnB,uCAAuC;YACvC,aAAa,OAAO,CAClB,eACA,KAAK,SAAS,CAAC;gBACb,OAAO,SAAS,MAAM,GAAG,IAAI,WAAW;gBACxC,aAAa,IAAI,OAAO,WAAW;YACrC;YAGF,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC,eAAe,CAAC;YACpE,OAAO;gBAAE,SAAS;gBAAM,OAAO,SAAS,MAAM;YAAC;QACjD,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,wCAAwC;QACxC,aAAa,UAAU,CAAC;QACxB;IACF;IAEA,MAAM,UAAU,CAAC;QACf,IAAI;YACF,yCAAyC;YACzC,MAAM,eAAe;mBAAI;gBAAO;aAAQ;YACxC,SAAS;YACT,eAAe,IAAI;YAEnB,uCAAuC;YACvC,aAAa,OAAO,CAClB,eACA,KAAK,SAAS,CAAC;gBACb,OAAO;gBACP,aAAa,IAAI,OAAO,WAAW;YACrC;YAGF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ,UAAU,EAAE;YAChE,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/igatpuri-dashboard.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, use<PERSON>em<PERSON> } from \"react\"\nimport {\n  Search,\n  Calendar,\n  Send,\n  Download,\n  Clock,\n  Bell,\n  MapPin,\n  Scale,\n  FileCheck,\n  CheckCircle2,\n  SortAsc,\n  SortDesc,\n  RefreshCw,\n  Filter,\n  ArrowLeft,\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from \"@/components/ui/tooltip\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from \"@/components/ui/dialog\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { updateCaseField } from \"@/lib/api\"\nimport { useCases } from \"@/hooks/use-cases\"\n\n\n\nexport default function IgatpuriDashboard() {\n  const { cases, loading, error, lastUpdated, updateCasesFromCsv, refreshCases, addCase } = useCases()\n\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [selectedType, setSelectedType] = useState(\"All Types\")\n  const [selectedStatus, setSelectedStatus] = useState(\"All Statuses\")\n  const [currentPage, setCurrentPage] = useState(1)\n  const [sortField, setSortField] = useState<string | null>(null)\n  const [sortDirection, setSortDirection] = useState<\"asc\" | \"desc\">(\"asc\")\n  const [casesPerPage, setCasesPerPage] = useState(10)\n  const [editableStatuses, setEditableStatuses] = useState<{[key: string]: string}>({})\n  const [receivedStatuses, setReceivedStatuses] = useState<{[key: string]: string}>({})\n  const [nextDates, setNextDates] = useState<{[key: string]: string}>({})\n  const [statusDialogOpen, setStatusDialogOpen] = useState<{[key: string]: boolean}>({})\n  const [tempStatusValue, setTempStatusValue] = useState(\"\")\n  const [currentEditingCase, setCurrentEditingCase] = useState<string | null>(null)\n  const [addCaseDialogOpen, setAddCaseDialogOpen] = useState(false)\n  const [newCase, setNewCase] = useState({\n    caseNumber: \"\",\n    appellant: \"\",\n    respondent: \"\",\n    caseType: \"अपील\"\n  })\n\n  // Filter cases for Igatpuri only\n  const igatpuriCases = useMemo(() => {\n    console.log('Total cases received:', cases.length)\n    console.log('Cases data:', cases)\n    const filtered = cases.filter((case_) => {\n      console.log(`Case taluka: \"${case_.taluka}\", matches Igatpuri:`, case_.taluka === \"Igatpuri\")\n      return case_.taluka === \"Igatpuri\"\n    })\n    console.log('Filtered Igatpuri cases:', filtered.length)\n    return filtered\n  }, [cases])\n\n  // Get unique values for filters\n  const caseTypes = useMemo(() => {\n    const types = [\"All Types\", ...new Set(igatpuriCases.map((c) => c.caseType).filter(Boolean))]\n    return types\n  }, [igatpuriCases])\n\n  const statuses = useMemo(() => {\n    const receivedValues = igatpuriCases.map((c) => receivedStatuses[c.caseNumber] || c.received).filter(Boolean)\n    const statusList = [\"All Statuses\", ...new Set(receivedValues)]\n    return statusList\n  }, [igatpuriCases, receivedStatuses])\n\n\n\n  // Get today's date for comparison\n  const today = new Date()\n\n  // Enhanced filter and search logic\n  const filteredCases = useMemo(() => {\n    const filtered = igatpuriCases.filter((case_) => {\n      const matchesSearch =\n        case_.caseNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        case_.appellant?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        case_.respondent?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        case_.caseType?.toLowerCase().includes(searchTerm.toLowerCase())\n\n      const matchesType = selectedType === \"All Types\" || case_.caseType === selectedType\n      const matchesStatus = selectedStatus === \"All Statuses\" ||\n        (receivedStatuses[case_.caseNumber] || case_.received) === selectedStatus\n\n      return matchesSearch && matchesType && matchesStatus\n    })\n\n    // Apply sorting\n    if (sortField) {\n      filtered.sort((a, b) => {\n        let aValue: string | number = a[sortField as keyof typeof a]\n        let bValue: string | number = b[sortField as keyof typeof b]\n\n        if (sortField === \"date\") {\n          aValue = new Date(aValue as string).getTime()\n          bValue = new Date(bValue as string).getTime()\n        }\n\n        if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1\n        if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1\n        return 0\n      })\n    }\n\n    return filtered\n  }, [igatpuriCases, searchTerm, selectedType, selectedStatus, sortField, sortDirection])\n\n  // Pagination logic\n  const totalPages = Math.ceil(filteredCases.length / casesPerPage)\n  const startIndex = (currentPage - 1) * casesPerPage\n  const paginatedCases = filteredCases.slice(startIndex, startIndex + casesPerPage)\n\n  // Statistics calculations\n  const stats = useMemo(() => {\n    const total = filteredCases.length\n    const receivedCount = filteredCases.filter((c) =>\n      (receivedStatuses[c.caseNumber] || c.received) === \"प्राप्त\"\n    ).length\n    const nextDateCount = filteredCases.filter((c) =>\n      (nextDates[c.caseNumber] || c.nextDate || \"\").trim() !== \"\"\n    ).length\n\n    return {\n      total,\n      received: receivedCount,\n      nextDate: nextDateCount\n    }\n  }, [filteredCases, receivedStatuses, nextDates])\n\n  // Handle sorting\n  const handleSort = (field: string) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\")\n    } else {\n      setSortField(field)\n      setSortDirection(\"asc\")\n    }\n    setCurrentPage(1)\n  }\n\n  // Handle page change\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page)\n  }\n\n  // Handle status update\n  const handleStatusUpdate = async (caseNumber: string, newStatus: string) => {\n    // Update local state immediately for responsive UI\n    setEditableStatuses(prev => ({\n      ...prev,\n      [caseNumber]: newStatus\n    }))\n\n    // Persist to database\n    try {\n      const result = await updateCaseField(caseNumber, 'status', newStatus)\n      if (!result.success) {\n        console.error('Failed to update status:', result.error)\n        // Optionally show a toast notification here\n      }\n    } catch (error) {\n      console.error('Error updating status:', error)\n    }\n  }\n\n  // Handle received status update\n  const handleReceivedUpdate = async (caseNumber: string, newReceived: string) => {\n    // Update local state immediately for responsive UI\n    setReceivedStatuses(prev => ({\n      ...prev,\n      [caseNumber]: newReceived\n    }))\n\n    // Persist to database\n    try {\n      const result = await updateCaseField(caseNumber, 'received', newReceived)\n      if (!result.success) {\n        console.error('Failed to update received status:', result.error)\n        // Optionally show a toast notification here\n      }\n    } catch (error) {\n      console.error('Error updating received status:', error)\n    }\n  }\n\n  // Handle next date update\n  const handleNextDateUpdate = async (caseNumber: string, newDate: string) => {\n    // Update local state immediately for responsive UI\n    setNextDates(prev => ({\n      ...prev,\n      [caseNumber]: newDate\n    }))\n\n    // Persist to database\n    try {\n      const result = await updateCaseField(caseNumber, 'next_date', newDate)\n      if (!result.success) {\n        console.error('Failed to update next date:', result.error)\n        // Optionally show a toast notification here\n      }\n    } catch (error) {\n      console.error('Error updating next date:', error)\n    }\n  }\n\n  // Handle status dialog\n  const openStatusDialog = (caseNumber: string, currentStatus: string) => {\n    setCurrentEditingCase(caseNumber)\n    setTempStatusValue(editableStatuses[caseNumber] || currentStatus || \"\")\n    setStatusDialogOpen(prev => ({ ...prev, [caseNumber]: true }))\n  }\n\n  const closeStatusDialog = (caseNumber: string) => {\n    setStatusDialogOpen(prev => ({ ...prev, [caseNumber]: false }))\n    setCurrentEditingCase(null)\n    setTempStatusValue(\"\")\n  }\n\n  const saveStatusDialog = (caseNumber: string) => {\n    handleStatusUpdate(caseNumber, tempStatusValue)\n    closeStatusDialog(caseNumber)\n  }\n\n  // Handle add new case\n  const handleAddNewCase = () => {\n    if (!newCase.caseNumber || !newCase.appellant || !newCase.respondent) {\n      alert(\"Please fill in all required fields\")\n      return\n    }\n\n    const caseData = {\n      date: new Date().toISOString().split(\"T\")[0],\n      caseType: newCase.caseType,\n      caseNumber: newCase.caseNumber,\n      appellant: newCase.appellant,\n      respondent: newCase.respondent,\n      received: \"प्राप्त\",\n      nextDate: \"2025-07-17\",\n      status: \"\",\n      taluka: \"Igatpuri\",\n      filedDate: new Date().toISOString().split(\"T\")[0],\n      lastUpdate: new Date().toISOString().split(\"T\")[0],\n    }\n\n    // Add the case using the addCase function from useCases hook\n    const result = addCase(caseData)\n\n    if (result.success) {\n      // Reset form and close dialog\n      setNewCase({\n        caseNumber: \"\",\n        appellant: \"\",\n        respondent: \"\",\n        caseType: \"अपील\"\n      })\n      setAddCaseDialogOpen(false)\n\n      // Show success message\n      alert(\"Case added successfully!\")\n    } else {\n      alert(`Failed to add case: ${result.error}`)\n    }\n  }\n\n  // Handle export\n  const handleExport = (format: string) => {\n    const dataToExport = filteredCases.map((case_) => ({\n      Date: case_.date,\n      \"Case Type\": case_.caseType,\n      \"Case Number\": case_.caseNumber,\n      Year: case_.year,\n      Appellant: case_.appellant,\n      Respondent: case_.respondent,\n      Received: receivedStatuses[case_.caseNumber] || case_.received || \"-\",\n      Status: editableStatuses[case_.caseNumber] || case_.status || \"\",\n    }))\n\n    if (format === \"CSV\") {\n      const csvContent = [\n        Object.keys(dataToExport[0]).join(\",\"),\n        ...dataToExport.map((row) => Object.values(row).join(\",\")),\n      ].join(\"\\n\")\n\n      const blob = new Blob([csvContent], { type: \"text/csv\" })\n      const url = window.URL.createObjectURL(blob)\n      const a = document.createElement(\"a\")\n      a.href = url\n      a.download = `igatpuri-cases-${new Date().toISOString().split(\"T\")[0]}.csv`\n      a.click()\n      window.URL.revokeObjectURL(url)\n    } else {\n      alert(`${format} export functionality would be implemented here`)\n    }\n  }\n\n  // Handle clear filters\n  const handleClearFilters = () => {\n    setSearchTerm(\"\")\n    setSelectedType(\"All Types\")\n    setSelectedStatus(\"All Statuses\")\n    setCurrentPage(1)\n    setSortField(null)\n  }\n\n  // Format date for display\n  const formatDate = (dateString: string) => {\n    if (!dateString) return \"N/A\"\n    return new Date(dateString).toLocaleDateString(\"en-IN\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\",\n    })\n  }\n\n  // Get status badge variant and icon\n  const getStatusInfo = (status: string) => {\n    const statusLower = status?.toLowerCase() || \"\"\n\n    if (statusLower.includes(\"completed\")) {\n      return { variant: \"default\" as const, icon: CheckCircle2, color: \"text-green-700\" }\n    } else if (statusLower.includes(\"received\")) {\n      return { variant: \"secondary\" as const, icon: FileCheck, color: \"text-orange-700\" }\n    } else if (statusLower.includes(\"scheduled\")) {\n      return { variant: \"outline\" as const, icon: Calendar, color: \"text-amber-700\" }\n    } else if (statusLower.includes(\"review\")) {\n      return { variant: \"secondary\" as const, icon: Clock, color: \"text-yellow-700\" }\n    } else if (statusLower.includes(\"issued\")) {\n      return { variant: \"outline\" as const, icon: Send, color: \"text-red-700\" }\n    } else {\n      return { variant: \"outline\" as const, icon: Clock, color: \"text-gray-700\" }\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-orange-50/20 to-amber-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-orange-600\" />\n          <p className=\"text-orange-700\">Loading cases...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <TooltipProvider>\n      <div className=\"min-h-screen bg-gradient-to-br from-orange-50/20 to-amber-50/20\">\n        <div className=\"mx-auto max-w-7xl space-y-4 p-4\">\n          {/* Header */}\n          <div className=\"text-center space-y-2 pb-4 border-b bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm border-orange-100\">\n            <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => window.location.href = '/'}\n                className=\"flex items-center gap-2 text-orange-700 hover:text-orange-900 hover:bg-orange-100 self-start sm:self-center\"\n              >\n                <ArrowLeft className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">Back to Home</span>\n                <span className=\"sm:hidden\">Back</span>\n              </Button>\n              <div className=\"flex flex-col sm:flex-row items-center gap-3\">\n                <div className=\"p-2 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg\">\n                  <Scale className=\"h-5 w-5 text-white\" />\n                </div>\n                <div className=\"text-center sm:text-left\">\n                  <h1 className=\"text-lg sm:text-xl font-bold text-orange-900\">Igatpuri Legal Case Dashboard</h1>\n                  <p className=\"text-xs sm:text-sm text-orange-700\">Sub-Divisional Magistrate Office, Nashik</p>\n                </div>\n              </div>\n              <div className=\"hidden sm:block w-24\"></div> {/* Spacer for centering on desktop */}\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-6 text-xs text-orange-600\">\n              <div className=\"flex items-center gap-1\">\n                <MapPin className=\"h-3 w-3\" />\n                <span>Igatpuri Subdivision</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <Calendar className=\"h-3 w-3\" />\n                <span className=\"text-center\">\n                  {today.toLocaleDateString(\"en-IN\", {\n                    weekday: \"long\",\n                    year: \"numeric\",\n                    month: \"long\",\n                    day: \"numeric\",\n                  })}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Statistics */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4\">\n            <Card className=\"border border-orange-100 shadow-sm bg-gradient-to-br from-orange-50/50 to-white\">\n              <CardContent className=\"p-3 sm:p-4 text-center\">\n                <div className=\"text-xl sm:text-2xl font-bold text-orange-600 mb-1\">{stats.total}</div>\n                <div className=\"text-xs text-orange-700\">Total Cases</div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"border border-green-100 shadow-sm bg-gradient-to-br from-green-50/50 to-white\">\n              <CardContent className=\"p-3 sm:p-4 text-center\">\n                <div className=\"text-xl sm:text-2xl font-bold text-green-600 mb-1\">{stats.received}</div>\n                <div className=\"text-xs text-green-700\">Received</div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"border border-blue-100 shadow-sm bg-gradient-to-br from-blue-50/50 to-white\">\n              <CardContent className=\"p-3 sm:p-4 text-center\">\n                <div className=\"text-xl sm:text-2xl font-bold text-blue-600 mb-1\">{stats.nextDate}</div>\n                <div className=\"text-xs text-blue-700\">Next Date</div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"space-y-4\">\n              {/* Search and Filters */}\n              <Card className=\"border border-orange-100 shadow-sm bg-white/80 backdrop-blur-sm\">\n                <CardContent className=\"p-4 space-y-4\">\n                  {/* Search Bar */}\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-400 h-4 w-4\" />\n                    <Input\n                      placeholder=\"Search by case number, appellant, respondent, or type...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10 h-10 border-orange-200 focus:border-orange-500 bg-white/50\"\n                    />\n                  </div>\n\n                  {/* Filters */}\n                  <div className=\"flex flex-col sm:flex-row gap-3 flex-wrap\">\n                    <div className=\"flex flex-col sm:flex-row gap-3 flex-1\">\n                      <Select value={selectedType} onValueChange={setSelectedType}>\n                        <SelectTrigger className=\"h-9 text-sm border-orange-200 bg-white/50 min-w-[120px]\">\n                          <SelectValue placeholder=\"Type\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {caseTypes.map((type) => (\n                            <SelectItem key={type} value={type}>\n                              {type}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n\n                      <Select value={selectedStatus} onValueChange={setSelectedStatus}>\n                        <SelectTrigger className=\"h-9 text-sm border-orange-200 bg-white/50 min-w-[120px]\">\n                          <SelectValue placeholder=\"Status\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {statuses.map((status) => (\n                            <SelectItem key={status} value={status}>\n                              {status}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n\n\n                    </div>\n\n                    <div className=\"flex gap-2 flex-wrap\">\n                      <Button\n                        onClick={() => setAddCaseDialogOpen(true)}\n                        className=\"h-9 text-sm bg-orange-600 hover:bg-orange-700 text-white\"\n                      >\n                        <span className=\"text-lg mr-1\">+</span>\n                        <span className=\"hidden sm:inline\">Add New Case</span>\n                        <span className=\"sm:hidden\">Add</span>\n                      </Button>\n\n                      <Button\n                        variant=\"outline\"\n                        onClick={handleClearFilters}\n                        className=\"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50\"\n                      >\n                        <Filter className=\"h-3 w-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">Clear</span>\n                        <span className=\"sm:hidden\">Clear</span>\n                      </Button>\n\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button\n                            variant=\"outline\"\n                            className=\"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50\"\n                          >\n                            <Download className=\"h-3 w-3 mr-1\" />\n                            <span className=\"hidden sm:inline\">Export</span>\n                            <span className=\"sm:hidden\">Export</span>\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent>\n                          <DropdownMenuItem onClick={() => handleExport(\"CSV\")}>Export as CSV</DropdownMenuItem>\n                          <DropdownMenuItem onClick={() => handleExport(\"PDF\")}>Export as PDF</DropdownMenuItem>\n                          <DropdownMenuItem onClick={() => handleExport(\"Excel\")}>Export as Excel</DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n\n                      <Button\n                        variant=\"outline\"\n                        onClick={refreshCases}\n                        disabled={loading}\n                        className=\"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50\"\n                      >\n                        <RefreshCw className={`h-3 w-3 mr-1 ${loading ? \"animate-spin\" : \"\"}`} />\n                        <span className=\"hidden sm:inline\">Refresh</span>\n                        <span className=\"sm:hidden\">Refresh</span>\n                      </Button>\n                    </div>\n                  </div>\n\n                  {/* Results Info */}\n                  <div className=\"flex items-center justify-between text-sm text-orange-700\">\n                    <span>\n                      Showing {paginatedCases.length} of {filteredCases.length} cases\n                    </span>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Cases Table */}\n              <Card className=\"border border-orange-100 shadow-sm bg-white/80 backdrop-blur-sm\">\n                <CardContent className=\"p-0\">\n                  <div className=\"overflow-x-auto min-w-full\">\n                    <Table className=\"min-w-[800px]\">\n                      <TableHeader>\n                        <TableRow className=\"border-b border-orange-100\">\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[100px]\">Case Type</TableHead>\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[140px]\">\n                            <Button\n                              variant=\"ghost\"\n                              onClick={() => handleSort(\"caseNumber\")}\n                              className=\"h-auto p-0 font-semibold hover:bg-orange-50\"\n                            >\n                              Case Number\n                              {sortField === \"caseNumber\" &&\n                                (sortDirection === \"asc\" ? (\n                                  <SortAsc className=\"ml-1 h-3 w-3\" />\n                                ) : (\n                                  <SortDesc className=\"ml-1 h-3 w-3\" />\n                                ))}\n                            </Button>\n                          </TableHead>\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[120px]\">Appellant</TableHead>\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[120px]\">Respondent</TableHead>\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[80px]\">Received</TableHead>\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[140px]\">Next Date</TableHead>\n                          <TableHead className=\"font-semibold text-orange-900 min-w-[120px]\">Status</TableHead>\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {paginatedCases.map((case_, index) => {\n                          const statusInfo = getStatusInfo(case_.status)\n                          const StatusIcon = statusInfo.icon\n\n                          return (\n                            <TableRow key={`${case_.caseNumber}-${index}`} className=\"hover:bg-orange-50/50\">\n                              <TableCell className=\"p-2 sm:p-4\">\n                                <Badge variant=\"outline\" className=\"text-xs border-orange-200 text-orange-700 whitespace-nowrap\">\n                                  {case_.caseType}\n                                </Badge>\n                              </TableCell>\n                              <TableCell className=\"p-2 sm:p-4\">\n                                <div className=\"font-semibold text-sm break-all\">{case_.caseNumber}</div>\n                              </TableCell>\n                              <TableCell className=\"p-2 sm:p-4\">\n                                <div className=\"font-medium text-sm break-words\">{case_.appellant}</div>\n                              </TableCell>\n                              <TableCell className=\"p-2 sm:p-4\">\n                                <div className=\"text-sm break-words\">{case_.respondent}</div>\n                              </TableCell>\n                              <TableCell className=\"p-2 sm:p-4\">\n                                <Select\n                                  value={receivedStatuses[case_.caseNumber] || case_.received || \"-\"}\n                                  onValueChange={(value) => handleReceivedUpdate(case_.caseNumber, value)}\n                                >\n                                  <SelectTrigger className=\"w-full min-w-[70px] h-8 text-xs border-orange-200\">\n                                    <SelectValue />\n                                  </SelectTrigger>\n                                  <SelectContent>\n                                    <SelectItem value=\"प्राप्त\">प्राप्त</SelectItem>\n                                    <SelectItem value=\"-\">-</SelectItem>\n                                  </SelectContent>\n                                </Select>\n                              </TableCell>\n                              <TableCell className=\"p-2 sm:p-4\">\n                                <Input\n                                  type=\"date\"\n                                  value={nextDates[case_.caseNumber] || case_.nextDate || \"2025-07-17\"}\n                                  onChange={(e) => handleNextDateUpdate(case_.caseNumber, e.target.value)}\n                                  className=\"w-full min-w-[130px] h-8 text-xs border-orange-200 focus:border-orange-400\"\n                                />\n                              </TableCell>\n                              <TableCell>\n                                <Dialog\n                                  open={statusDialogOpen[case_.caseNumber] || false}\n                                  onOpenChange={(open) => {\n                                    if (!open) closeStatusDialog(case_.caseNumber)\n                                  }}\n                                >\n                                  <DialogTrigger asChild>\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      className=\"text-xs h-8 min-w-[120px] justify-start\"\n                                      onClick={() => openStatusDialog(case_.caseNumber, case_.status)}\n                                    >\n                                      {(editableStatuses[case_.caseNumber] || case_.status || \"Enter status...\").substring(0, 15)}\n                                      {(editableStatuses[case_.caseNumber] || case_.status || \"\").length > 15 ? \"...\" : \"\"}\n                                    </Button>\n                                  </DialogTrigger>\n                                  <DialogContent className=\"sm:max-w-[425px]\">\n                                    <DialogHeader>\n                                      <DialogTitle>Edit Status - Case #{case_.caseNumber}</DialogTitle>\n                                    </DialogHeader>\n                                    <div className=\"grid gap-4 py-4\">\n                                      <Textarea\n                                        value={tempStatusValue}\n                                        onChange={(e) => setTempStatusValue(e.target.value)}\n                                        placeholder=\"Enter detailed status information...\"\n                                        className=\"min-h-[100px]\"\n                                      />\n                                    </div>\n                                    <div className=\"flex justify-end gap-2\">\n                                      <Button variant=\"outline\" onClick={() => closeStatusDialog(case_.caseNumber)}>\n                                        Cancel\n                                      </Button>\n                                      <Button onClick={() => saveStatusDialog(case_.caseNumber)}>\n                                        Save\n                                      </Button>\n                                    </div>\n                                  </DialogContent>\n                                </Dialog>\n                              </TableCell>\n                            </TableRow>\n                          )\n                        })}\n                      </TableBody>\n                    </Table>\n                  </div>\n\n                  {/* Pagination */}\n                  {totalPages > 1 && (\n                    <div className=\"flex flex-col sm:flex-row items-center justify-between gap-3 p-4 border-t border-orange-100\">\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-sm text-orange-700\">\n                          Page {currentPage} of {totalPages}\n                        </span>\n                        <Select\n                          value={casesPerPage.toString()}\n                          onValueChange={(value) => {\n                            setCasesPerPage(Number(value))\n                            setCurrentPage(1)\n                          }}\n                        >\n                          <SelectTrigger className=\"w-20 h-8 border-orange-200\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"5\">5</SelectItem>\n                            <SelectItem value=\"10\">10</SelectItem>\n                            <SelectItem value=\"20\">20</SelectItem>\n                            <SelectItem value=\"50\">50</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handlePageChange(currentPage - 1)}\n                          disabled={currentPage === 1}\n                          className=\"border-orange-200 hover:bg-orange-50\"\n                        >\n                          Previous\n                        </Button>\n                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                          const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i\n                          return (\n                            <Button\n                              key={page}\n                              variant={page === currentPage ? \"default\" : \"outline\"}\n                              size=\"sm\"\n                              onClick={() => handlePageChange(page)}\n                              className={`w-8 h-8 p-0 ${page === currentPage ? \"bg-orange-600 hover:bg-orange-700\" : \"border-orange-200 hover:bg-orange-50\"}`}\n                            >\n                              {page}\n                            </Button>\n                          )\n                        })}\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handlePageChange(currentPage + 1)}\n                          disabled={currentPage === totalPages}\n                          className=\"border-orange-200 hover:bg-orange-50\"\n                        >\n                          Next\n                        </Button>\n                      </div>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Add New Case Dialog */}\n      <Dialog open={addCaseDialogOpen} onOpenChange={setAddCaseDialogOpen}>\n        <DialogContent className=\"sm:max-w-[500px]\">\n          <DialogHeader>\n            <DialogTitle>Add New Case - Igatpuri</DialogTitle>\n          </DialogHeader>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <label htmlFor=\"caseType\" className=\"text-right text-sm font-medium\">\n                Case Type *\n              </label>\n              <Select\n                value={newCase.caseType}\n                onValueChange={(value) => setNewCase(prev => ({ ...prev, caseType: value }))}\n              >\n                <SelectTrigger className=\"col-span-3\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"अपील\">अपील</SelectItem>\n                  <SelectItem value=\"रिव्हीजन\">रिव्हीजन</SelectItem>\n                  <SelectItem value=\"मामलेदार कोर्ट\">मामलेदार कोर्ट</SelectItem>\n                  <SelectItem value=\"गौणखनिज\">गौणखनिज</SelectItem>\n                  <SelectItem value=\"अतिक्रमण\">अतिक्रमण</SelectItem>\n                  <SelectItem value=\"कुळ कायदा\">कुळ कायदा</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <label htmlFor=\"caseNumber\" className=\"text-right text-sm font-medium\">\n                Case Number *\n              </label>\n              <Input\n                id=\"caseNumber\"\n                value={newCase.caseNumber}\n                onChange={(e) => setNewCase(prev => ({ ...prev, caseNumber: e.target.value }))}\n                className=\"col-span-3\"\n                placeholder=\"e.g., अपील/150/2023\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <label htmlFor=\"appellant\" className=\"text-right text-sm font-medium\">\n                Appellant *\n              </label>\n              <Input\n                id=\"appellant\"\n                value={newCase.appellant}\n                onChange={(e) => setNewCase(prev => ({ ...prev, appellant: e.target.value }))}\n                className=\"col-span-3\"\n                placeholder=\"Appellant name\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <label htmlFor=\"respondent\" className=\"text-right text-sm font-medium\">\n                Respondent *\n              </label>\n              <Input\n                id=\"respondent\"\n                value={newCase.respondent}\n                onChange={(e) => setNewCase(prev => ({ ...prev, respondent: e.target.value }))}\n                className=\"col-span-3\"\n                placeholder=\"Respondent name\"\n              />\n            </div>\n          </div>\n          <div className=\"flex justify-end gap-2\">\n            <Button variant=\"outline\" onClick={() => setAddCaseDialogOpen(false)}>\n              Cancel\n            </Button>\n            <Button onClick={handleAddNewCase} className=\"bg-orange-600 hover:bg-orange-700\">\n              Add Case\n            </Button>\n          </div>\n        </DialogContent>\n      </Dialog>\n    </TooltipProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;AAmCe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAEjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IACpF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;IACZ;IAEA,iCAAiC;IACjC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,QAAQ,GAAG,CAAC,yBAAyB,MAAM,MAAM;QACjD,QAAQ,GAAG,CAAC,eAAe;QAC3B,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC;YAC7B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,MAAM,CAAC,oBAAoB,CAAC,EAAE,MAAM,MAAM,KAAK;YAClF,OAAO,MAAM,MAAM,KAAK;QAC1B;QACA,QAAQ,GAAG,CAAC,4BAA4B,SAAS,MAAM;QACvD,OAAO;IACT,GAAG;QAAC;KAAM;IAEV,gCAAgC;IAChC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,QAAQ;YAAC;eAAgB,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;SAAU;QAC7F,OAAO;IACT,GAAG;QAAC;KAAc;IAElB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAC,IAAM,gBAAgB,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC;QACrG,MAAM,aAAa;YAAC;eAAmB,IAAI,IAAI;SAAgB;QAC/D,OAAO;IACT,GAAG;QAAC;QAAe;KAAiB;IAIpC,kCAAkC;IAClC,MAAM,QAAQ,IAAI;IAElB,mCAAmC;IACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,WAAW,cAAc,MAAM,CAAC,CAAC;YACrC,MAAM,gBACJ,MAAM,UAAU,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,MAAM,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC9D,MAAM,UAAU,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,MAAM,QAAQ,EAAE,cAAc,SAAS,WAAW,WAAW;YAE/D,MAAM,cAAc,iBAAiB,eAAe,MAAM,QAAQ,KAAK;YACvE,MAAM,gBAAgB,mBAAmB,kBACvC,CAAC,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,QAAQ,MAAM;YAE7D,OAAO,iBAAiB,eAAe;QACzC;QAEA,gBAAgB;QAChB,IAAI,WAAW;YACb,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,IAAI,SAA0B,CAAC,CAAC,UAA4B;gBAC5D,IAAI,SAA0B,CAAC,CAAC,UAA4B;gBAE5D,IAAI,cAAc,QAAQ;oBACxB,SAAS,IAAI,KAAK,QAAkB,OAAO;oBAC3C,SAAS,IAAI,KAAK,QAAkB,OAAO;gBAC7C;gBAEA,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;gBAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;gBAC3D,OAAO;YACT;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAe;QAAY;QAAc;QAAgB;QAAW;KAAc;IAEtF,mBAAmB;IACnB,MAAM,aAAa,KAAK,IAAI,CAAC,cAAc,MAAM,GAAG;IACpD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,iBAAiB,cAAc,KAAK,CAAC,YAAY,aAAa;IAEpE,0BAA0B;IAC1B,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,QAAQ,cAAc,MAAM;QAClC,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAC,IAC1C,CAAC,gBAAgB,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,QAAQ,MAAM,WACnD,MAAM;QACR,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAC,IAC1C,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,QAAQ,IAAI,EAAE,EAAE,IAAI,OAAO,IACzD,MAAM;QAER,OAAO;YACL;YACA,UAAU;YACV,UAAU;QACZ;IACF,GAAG;QAAC;QAAe;QAAkB;KAAU;IAE/C,iBAAiB;IACjB,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;QACA,eAAe;IACjB;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OAAO,YAAoB;QACpD,mDAAmD;QACnD,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QAED,sBAAsB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0GAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,UAAU;YAC3D,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,QAAQ,KAAK,CAAC,4BAA4B,OAAO,KAAK;YACtD,4CAA4C;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,gCAAgC;IAChC,MAAM,uBAAuB,OAAO,YAAoB;QACtD,mDAAmD;QACnD,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QAED,sBAAsB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0GAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,YAAY;YAC7D,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,QAAQ,KAAK,CAAC,qCAAqC,OAAO,KAAK;YAC/D,4CAA4C;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,OAAO,YAAoB;QACtD,mDAAmD;QACnD,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QAED,sBAAsB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0GAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,aAAa;YAC9D,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,QAAQ,KAAK,CAAC,+BAA+B,OAAO,KAAK;YACzD,4CAA4C;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC,YAAoB;QAC5C,sBAAsB;QACtB,mBAAmB,gBAAgB,CAAC,WAAW,IAAI,iBAAiB;QACpE,oBAAoB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,WAAW,EAAE;YAAK,CAAC;IAC9D;IAEA,MAAM,oBAAoB,CAAC;QACzB,oBAAoB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,WAAW,EAAE;YAAM,CAAC;QAC7D,sBAAsB;QACtB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,mBAAmB,YAAY;QAC/B,kBAAkB;IACpB;IAEA,sBAAsB;IACtB,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,UAAU,IAAI,CAAC,QAAQ,SAAS,IAAI,CAAC,QAAQ,UAAU,EAAE;YACpE,MAAM;YACN;QACF;QAEA,MAAM,WAAW;YACf,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,UAAU,QAAQ,QAAQ;YAC1B,YAAY,QAAQ,UAAU;YAC9B,WAAW,QAAQ,SAAS;YAC5B,YAAY,QAAQ,UAAU;YAC9B,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjD,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD;QAEA,6DAA6D;QAC7D,MAAM,SAAS,QAAQ;QAEvB,IAAI,OAAO,OAAO,EAAE;YAClB,8BAA8B;YAC9B,WAAW;gBACT,YAAY;gBACZ,WAAW;gBACX,YAAY;gBACZ,UAAU;YACZ;YACA,qBAAqB;YAErB,uBAAuB;YACvB,MAAM;QACR,OAAO;YACL,MAAM,CAAC,oBAAoB,EAAE,OAAO,KAAK,EAAE;QAC7C;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,MAAM,eAAe,cAAc,GAAG,CAAC,CAAC,QAAU,CAAC;gBACjD,MAAM,MAAM,IAAI;gBAChB,aAAa,MAAM,QAAQ;gBAC3B,eAAe,MAAM,UAAU;gBAC/B,MAAM,MAAM,IAAI;gBAChB,WAAW,MAAM,SAAS;gBAC1B,YAAY,MAAM,UAAU;gBAC5B,UAAU,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,QAAQ,IAAI;gBAClE,QAAQ,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,MAAM,IAAI;YAChE,CAAC;QAED,IAAI,WAAW,OAAO;YACpB,MAAM,aAAa;gBACjB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC;mBAC/B,aAAa,GAAG,CAAC,CAAC,MAAQ,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC;aACtD,CAAC,IAAI,CAAC;YAEP,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAW;YACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC3E,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,OAAO;YACL,MAAM,GAAG,OAAO,+CAA+C,CAAC;QAClE;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,cAAc;QACd,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,aAAa;IACf;IAEA,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,QAAQ,iBAAiB;QAE7C,IAAI,YAAY,QAAQ,CAAC,cAAc;YACrC,OAAO;gBAAE,SAAS;gBAAoB,MAAM,qNAAA,CAAA,eAAY;gBAAE,OAAO;YAAiB;QACpF,OAAO,IAAI,YAAY,QAAQ,CAAC,aAAa;YAC3C,OAAO;gBAAE,SAAS;gBAAsB,MAAM,gNAAA,CAAA,YAAS;gBAAE,OAAO;YAAkB;QACpF,OAAO,IAAI,YAAY,QAAQ,CAAC,cAAc;YAC5C,OAAO;gBAAE,SAAS;gBAAoB,MAAM,0MAAA,CAAA,WAAQ;gBAAE,OAAO;YAAiB;QAChF,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW;YACzC,OAAO;gBAAE,SAAS;gBAAsB,MAAM,oMAAA,CAAA,QAAK;gBAAE,OAAO;YAAkB;QAChF,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW;YACzC,OAAO;gBAAE,SAAS;gBAAoB,MAAM,kMAAA,CAAA,OAAI;gBAAE,OAAO;YAAe;QAC1E,OAAO;YACL,OAAO;gBAAE,SAAS;gBAAoB,MAAM,oMAAA,CAAA,QAAK;gBAAE,OAAO;YAAgB;QAC5E;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAE,WAAU;kCAAkB;;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,8OAAC,4HAAA,CAAA,kBAAe;;0BACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;sDAE9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA+C;;;;;;sEAC7D,8OAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CAAI,WAAU;;;;;;wCAA6B;;;;;;;8CAG9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DACb,MAAM,kBAAkB,CAAC,SAAS;wDACjC,SAAS;wDACT,MAAM;wDACN,OAAO;wDACP,KAAK;oDACP;;;;;;;;;;;;;;;;;;;;;;;;sCAOR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAsD,MAAM,KAAK;;;;;;0DAChF,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;8CAI7C,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAqD,MAAM,QAAQ;;;;;;0DAClF,8OAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;;;;;;8CAI5C,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DAAoD,MAAM,QAAQ;;;;;;0DACjF,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAI,WAAU;;8CAEX,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,0HAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2HAAA,CAAA,SAAM;gEAAC,OAAO;gEAAc,eAAe;;kFAC1C,8OAAC,2HAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,8OAAC,2HAAA,CAAA,gBAAa;kFACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,2HAAA,CAAA,aAAU;gFAAY,OAAO;0FAC3B;+EADc;;;;;;;;;;;;;;;;0EAOvB,8OAAC,2HAAA,CAAA,SAAM;gEAAC,OAAO;gEAAgB,eAAe;;kFAC5C,8OAAC,2HAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,8OAAC,2HAAA,CAAA,gBAAa;kFACX,SAAS,GAAG,CAAC,CAAC,uBACb,8OAAC,2HAAA,CAAA,aAAU;gFAAc,OAAO;0FAC7B;+EADc;;;;;;;;;;;;;;;;;;;;;;kEAUzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAS,IAAM,qBAAqB;gEACpC,WAAU;;kFAEV,8OAAC;wEAAK,WAAU;kFAAe;;;;;;kFAC/B,8OAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,8OAAC;wEAAK,WAAU;kFAAY;;;;;;;;;;;;0EAG9B,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,SAAS;gEACT,WAAU;;kFAEV,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,8OAAC;wEAAK,WAAU;kFAAY;;;;;;;;;;;;0EAG9B,8OAAC,qIAAA,CAAA,eAAY;;kFACX,8OAAC,qIAAA,CAAA,sBAAmB;wEAAC,OAAO;kFAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,WAAU;;8FAEV,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;oFAAK,WAAU;8FAAmB;;;;;;8FACnC,8OAAC;oFAAK,WAAU;8FAAY;;;;;;;;;;;;;;;;;kFAGhC,8OAAC,qIAAA,CAAA,sBAAmB;;0FAClB,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,aAAa;0FAAQ;;;;;;0FACtD,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,aAAa;0FAAQ;;;;;;0FACtD,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,aAAa;0FAAU;;;;;;;;;;;;;;;;;;0EAI5D,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,SAAS;gEACT,UAAU;gEACV,WAAU;;kFAEV,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;kFACrE,8OAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,8OAAC;wEAAK,WAAU;kFAAY;;;;;;;;;;;;;;;;;;;;;;;;0DAMlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;wDAAK;wDACK,eAAe,MAAM;wDAAC;wDAAK,cAAc,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;;;;;8CAOjE,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,8OAAC,0HAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA8C;;;;;;kFACnE,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFACnB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS,IAAM,WAAW;4EAC1B,WAAU;;gFACX;gFAEE,cAAc,gBACb,CAAC,kBAAkB,sBACjB,8OAAC,8NAAA,CAAA,UAAO;oFAAC,WAAU;;;;;yGAEnB,8OAAC,iOAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;wFACrB;;;;;;;;;;;;kFAGP,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA8C;;;;;;kFACnE,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA8C;;;;;;kFACnE,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA6C;;;;;;kFAClE,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA8C;;;;;;kFACnE,8OAAC,0HAAA,CAAA,YAAS;wEAAC,WAAU;kFAA8C;;;;;;;;;;;;;;;;;sEAGvE,8OAAC,0HAAA,CAAA,YAAS;sEACP,eAAe,GAAG,CAAC,CAAC,OAAO;gEAC1B,MAAM,aAAa,cAAc,MAAM,MAAM;gEAC7C,MAAM,aAAa,WAAW,IAAI;gEAElC,qBACE,8OAAC,0HAAA,CAAA,WAAQ;oEAAsC,WAAU;;sFACvD,8OAAC,0HAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC,0HAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,MAAM,QAAQ;;;;;;;;;;;sFAGnB,8OAAC,0HAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC;gFAAI,WAAU;0FAAmC,MAAM,UAAU;;;;;;;;;;;sFAEpE,8OAAC,0HAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC;gFAAI,WAAU;0FAAmC,MAAM,SAAS;;;;;;;;;;;sFAEnE,8OAAC,0HAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC;gFAAI,WAAU;0FAAuB,MAAM,UAAU;;;;;;;;;;;sFAExD,8OAAC,0HAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC,2HAAA,CAAA,SAAM;gFACL,OAAO,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,QAAQ,IAAI;gFAC/D,eAAe,CAAC,QAAU,qBAAqB,MAAM,UAAU,EAAE;;kGAEjE,8OAAC,2HAAA,CAAA,gBAAa;wFAAC,WAAU;kGACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kGAEd,8OAAC,2HAAA,CAAA,gBAAa;;0GACZ,8OAAC,2HAAA,CAAA,aAAU;gGAAC,OAAM;0GAAU;;;;;;0GAC5B,8OAAC,2HAAA,CAAA,aAAU;gGAAC,OAAM;0GAAI;;;;;;;;;;;;;;;;;;;;;;;sFAI5B,8OAAC,0HAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC,0HAAA,CAAA,QAAK;gFACJ,MAAK;gFACL,OAAO,SAAS,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,QAAQ,IAAI;gFACxD,UAAU,CAAC,IAAM,qBAAqB,MAAM,UAAU,EAAE,EAAE,MAAM,CAAC,KAAK;gFACtE,WAAU;;;;;;;;;;;sFAGd,8OAAC,0HAAA,CAAA,YAAS;sFACR,cAAA,8OAAC,2HAAA,CAAA,SAAM;gFACL,MAAM,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI;gFAC5C,cAAc,CAAC;oFACb,IAAI,CAAC,MAAM,kBAAkB,MAAM,UAAU;gFAC/C;;kGAEA,8OAAC,2HAAA,CAAA,gBAAa;wFAAC,OAAO;kGACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,WAAU;4FACV,SAAS,IAAM,iBAAiB,MAAM,UAAU,EAAE,MAAM,MAAM;;gGAE7D,CAAC,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,MAAM,IAAI,iBAAiB,EAAE,SAAS,CAAC,GAAG;gGACvF,CAAC,gBAAgB,CAAC,MAAM,UAAU,CAAC,IAAI,MAAM,MAAM,IAAI,EAAE,EAAE,MAAM,GAAG,KAAK,QAAQ;;;;;;;;;;;;kGAGtF,8OAAC,2HAAA,CAAA,gBAAa;wFAAC,WAAU;;0GACvB,8OAAC,2HAAA,CAAA,eAAY;0GACX,cAAA,8OAAC,2HAAA,CAAA,cAAW;;wGAAC;wGAAqB,MAAM,UAAU;;;;;;;;;;;;0GAEpD,8OAAC;gGAAI,WAAU;0GACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oGACP,OAAO;oGACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oGAClD,aAAY;oGACZ,WAAU;;;;;;;;;;;0GAGd,8OAAC;gGAAI,WAAU;;kHACb,8OAAC,2HAAA,CAAA,SAAM;wGAAC,SAAQ;wGAAU,SAAS,IAAM,kBAAkB,MAAM,UAAU;kHAAG;;;;;;kHAG9E,8OAAC,2HAAA,CAAA,SAAM;wGAAC,SAAS,IAAM,iBAAiB,MAAM,UAAU;kHAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mEAvEtD,GAAG,MAAM,UAAU,CAAC,CAAC,EAAE,OAAO;;;;;4DAgFjD;;;;;;;;;;;;;;;;;4CAML,aAAa,mBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAA0B;oEAClC;oEAAY;oEAAK;;;;;;;0EAEzB,8OAAC,2HAAA,CAAA,SAAM;gEACL,OAAO,aAAa,QAAQ;gEAC5B,eAAe,CAAC;oEACd,gBAAgB,OAAO;oEACvB,eAAe;gEACjB;;kFAEA,8OAAC,2HAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,2HAAA,CAAA,gBAAa;;0FACZ,8OAAC,2HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAI;;;;;;0FACtB,8OAAC,2HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;0FACvB,8OAAC,2HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;0FACvB,8OAAC,2HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;kEAI7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB,cAAc;gEAC9C,UAAU,gBAAgB;gEAC1B,WAAU;0EACX;;;;;;4DAGA,MAAM,IAAI,CAAC;gEAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;4DAAY,GAAG,CAAC,GAAG;gEACnD,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,MAAM;gEACtE,qBACE,8OAAC,2HAAA,CAAA,SAAM;oEAEL,SAAS,SAAS,cAAc,YAAY;oEAC5C,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,WAAW,CAAC,YAAY,EAAE,SAAS,cAAc,sCAAsC,wCAAwC;8EAE9H;mEANI;;;;;4DASX;0EACA,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB,cAAc;gEAC9C,UAAU,gBAAgB;gEAC1B,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAanB,8OAAC,2HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC7C,cAAA,8OAAC,2HAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,2HAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,2HAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiC;;;;;;sDAGrE,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO,QAAQ,QAAQ;4CACvB,eAAe,CAAC,QAAU,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU;oDAAM,CAAC;;8DAE1E,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAiB;;;;;;sEACnC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAIpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAiC;;;;;;sDAGvE,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,UAAU;4CACzB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC5E,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAAiC;;;;;;sDAGtE,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC3E,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAiC;;;;;;sDAGvE,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,QAAQ,UAAU;4CACzB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC5E,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAQ;;;;;;8CAGtE,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAS;oCAAkB,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7F", "debugId": null}}, {"offset": {"line": 3452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/app/igatpuri/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport IgatpuriDashboard from \"../../igatpuri-dashboard\"\n\nexport default function IgatpuriPage() {\n  return <IgatpuriDashboard />\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,sHAAA,CAAA,UAAiB;;;;;AAC3B", "debugId": null}}]}