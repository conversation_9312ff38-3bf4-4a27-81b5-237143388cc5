{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/legal-case-dashboard/my-app/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Scale, MapPin, ArrowRight, FileText, Users, Clock, Shield } from \"lucide-react\"\n\n\nexport default function HomePage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      <div className=\"mx-auto max-w-6xl space-y-8 p-4 sm:p-6 lg:p-8\">\n        {/* Header */}\n        <div className=\"text-center space-y-6 py-8 sm:py-12\">\n          <div className=\"flex flex-col items-center gap-4 mb-6\">\n            <div className=\"p-4 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl shadow-lg\">\n              <Scale className=\"h-10 w-10 sm:h-12 sm:w-12 text-white\" />\n            </div>\n            <div className=\"space-y-2\">\n              <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                Legal Case Monitoring System\n              </h1>\n              <p className=\"text-lg sm:text-xl text-gray-600 font-medium\">Sub-Divisional Magistrate Office, Nashik</p>\n              <p className=\"text-sm sm:text-base text-gray-500 max-w-2xl mx-auto\">\n                Streamlined case management and monitoring for efficient legal proceedings across subdivisions\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Features Section */}\n        <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n          <div className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200\">\n            <FileText className=\"h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2\" />\n            <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Case Management</p>\n          </div>\n          <div className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200\">\n            <Users className=\"h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2\" />\n            <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Multi-User Access</p>\n          </div>\n          <div className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200\">\n            <Clock className=\"h-6 w-6 sm:h-8 sm:w-8 text-orange-600 mx-auto mb-2\" />\n            <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Real-time Updates</p>\n          </div>\n          <div className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200\">\n            <Shield className=\"h-6 w-6 sm:h-8 sm:w-8 text-purple-600 mx-auto mb-2\" />\n            <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Secure Platform</p>\n          </div>\n        </div>\n\n        {/* Dashboard Selection */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8\">\n          {/* Igatpuri Dashboard */}\n          <Card className=\"group border border-orange-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-orange-50 to-amber-50\">\n            <CardHeader className=\"text-center pb-6\">\n              <div className=\"p-4 bg-gradient-to-br from-orange-500 to-amber-500 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                <MapPin className=\"h-8 w-8 text-white\" />\n              </div>\n              <CardTitle className=\"text-xl sm:text-2xl font-bold text-gray-900 mb-2\">Igatpuri Subdivision</CardTitle>\n              <p className=\"text-sm sm:text-base text-gray-600 leading-relaxed\">\n                Monitor and manage legal cases for Igatpuri area with comprehensive tracking and reporting\n              </p>\n            </CardHeader>\n            <CardContent className=\"pt-0 pb-6\">\n              <Link href=\"/igatpuri\" className=\"block\">\n                <Button className=\"w-full bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\">\n                  Access Igatpuri Dashboard\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" />\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n\n          {/* Trimbakeshwar Dashboard */}\n          <Card className=\"group border border-indigo-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-indigo-50 to-purple-50\">\n            <CardHeader className=\"text-center pb-6\">\n              <div className=\"p-4 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                <MapPin className=\"h-8 w-8 text-white\" />\n              </div>\n              <CardTitle className=\"text-xl sm:text-2xl font-bold text-gray-900 mb-2\">Trimbakeshwar Subdivision</CardTitle>\n              <p className=\"text-sm sm:text-base text-gray-600 leading-relaxed\">\n                Monitor and manage legal cases for Trimbakeshwar area with comprehensive tracking and reporting\n              </p>\n            </CardHeader>\n            <CardContent className=\"pt-0 pb-6\">\n              <Link href=\"/trimbakeshwar\" className=\"block\">\n                <Button className=\"w-full bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\">\n                  Access Trimbakeshwar Dashboard\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300\" />\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center py-8 border-t border-gray-200 mt-12\">\n          <p className=\"text-sm text-gray-500\">\n            © 2024 Sub-Divisional Magistrate Office, Nashik. All rights reserved.\n          </p>\n          <p className=\"text-xs text-gray-400 mt-1\">\n            Powered by Legal Case Monitoring System\n          </p>\n        </div>\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwH;;;;;;kDAGtI,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;8BAQ1E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAKhE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;sDAAmD;;;;;;sDACxE,8OAAC;4CAAE,WAAU;sDAAqD;;;;;;;;;;;;8CAIpE,8OAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAC/B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,WAAU;;gDAA+L;8DAE/M,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;sDAAmD;;;;;;sDACxE,8OAAC;4CAAE,WAAU;sDAAqD;;;;;;;;;;;;8CAIpE,8OAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDACpC,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,WAAU;;gDAAiM;8DAEjN,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}]}