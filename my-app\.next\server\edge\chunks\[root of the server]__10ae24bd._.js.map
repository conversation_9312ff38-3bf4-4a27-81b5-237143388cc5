{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport function middleware(request: NextRequest) {\n  // Allow API routes to be accessed without authentication\n  if (request.nextUrl.pathname.startsWith('/api/')) {\n    return NextResponse.next()\n  }\n  \n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,yDAAyD;IACzD,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QAChD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}