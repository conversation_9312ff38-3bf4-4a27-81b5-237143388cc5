{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a1b8b59d._.js", "server/edge/chunks/[root of the server]__10ae24bd._.js", "server/edge/chunks/edge-wrapper_321a5c3e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Nnbq3LlphOz3Wcm+v/8K8Bf4KO8v4KFvFky9/wyi7uw=", "__NEXT_PREVIEW_MODE_ID": "474c21d5046e3dd39e84465bb6234b24", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a4dee293dbb8350563256d1934d0f6a457b8c36df3b9f49e733cd8e042ce8759", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "62a931f25507ef11796993bce91b54bb5f3d05d278ee6d53bf8277f3263d89dd"}}}, "sortedMiddleware": ["/"], "functions": {}}