"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[523],{3999:(e,t,a)=>{a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},5784:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>l,yv:()=>c});var s=a(5155);a(2115);var r=a(3081),n=a(6474),o=a(5196),i=a(7863),d=a(3999);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:o,...i}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[o,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:a,position:n="popper",...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...o,children:[(0,s.jsx)(f,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(v,{})]})})}function g(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}function v(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},7133:(e,t,a)=>{a.d(t,{SQ:()=>d,_2:()=>l,rI:()=>o,ty:()=>i});var s=a(5155);a(2115);var r=a(9449),n=a(3999);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t})}function d(e){let{className:t,sideOffset:a=4,...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...o})})}function l(e){let{className:t,inset:a,variant:o="default",...i}=e;return(0,s.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":o,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}},7168:(e,t,a)=>{a.d(t,{$:()=>d});var s=a(5155);a(2115);var r=a(9708),n=a(2085),o=a(3999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...l}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:n,className:t})),...l})}},7777:(e,t,a)=>{a.d(t,{Bc:()=>n});var s=a(5155);a(2115);var r=a(9613);function n(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}},7996:(e,t,a)=>{a.d(t,{L:()=>n});var s=a(2115);let r=[{date:"2024-01-15",caseType:"अपील",caseNumber:"अपील/150/2023",appellant:"लक्ष्मीबाई शेलार",respondent:"सुनीता शेलार",received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Igatpuri",filedDate:"2024-01-15",lastUpdate:"2024-01-15"},{date:"2024-01-16",caseType:"रिव्हीजन",caseNumber:"रिव्हीजन/139/2023",appellant:"चंद्रबाई हंबीर",respondent:"गंगुबाई आघाण",received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Igatpuri",filedDate:"2024-01-16",lastUpdate:"2024-01-16"},{date:"2024-01-17",caseType:"मामलेदार कोर्ट",caseNumber:"मामलेदार/131/2023",appellant:"अरुण पोरजे",respondent:"मनोज चौधरी",received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Trimbakeshwar",filedDate:"2024-01-17",lastUpdate:"2024-01-17"},{date:"2024-01-18",caseType:"गौणखनिज",caseNumber:"गौणखनिज/113/2023",appellant:"केरुजी काळे",respondent:"कोंडाजी भालेराव",received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Trimbakeshwar",filedDate:"2024-01-18",lastUpdate:"2024-01-18"},{date:"2024-01-19",caseType:"अतिक्रमण",caseNumber:"अतिक्रमण/104/2023",appellant:"रामभाऊ ढोन्नर",respondent:"अंबाबाई ढोन्नर उर्फ बिन्नर",received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Igatpuri",filedDate:"2024-01-19",lastUpdate:"2024-01-19"},{date:"2024-01-20",caseType:"कुळ कायदा",caseNumber:"कुळ/90/2023",appellant:"अनुसया मालुंजकर",respondent:"ओम मालुंजकर",received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Trimbakeshwar",filedDate:"2024-01-20",lastUpdate:"2024-01-20"}];function n(){let[e,t]=(0,s.useState)(r),[a,n]=(0,s.useState)(!1),[o,i]=(0,s.useState)(null),[d,l]=(0,s.useState)(new Date);(0,s.useEffect)(()=>{c()},[]),(0,s.useEffect)(()=>{let e=localStorage.getItem("legal-cases");if(e)try{let a=JSON.parse(e);a.cases&&Array.isArray(a.cases)&&(t(a.cases),l(new Date(a.lastUpdated)),console.log("Loaded ".concat(a.cases.length," cases from localStorage")))}catch(e){console.error("Error parsing saved cases:",e)}},[]);let c=async()=>{try{n(!0),i(null);try{console.log("Fetching cases from API...");let e=await fetch("/api/update-cases");if(console.log("API response status:",e.status),e.ok){let a=await e.json();if(console.log("API response data:",a),a.success&&a.cases){console.log("Successfully loaded ".concat(a.cases.length," cases from API")),t(a.cases),l(a.lastUpdated?new Date(a.lastUpdated):new Date),localStorage.setItem("legal-cases",JSON.stringify({cases:a.cases,lastUpdated:a.lastUpdated||new Date().toISOString()}));return}console.log("API response missing success or cases:",a)}else console.log("API response not ok:",e.status,e.statusText)}catch(e){console.log("API not available, falling back to localStorage:",e)}let e=localStorage.getItem("legal-cases");if(e){let a=JSON.parse(e);t(a.cases||r),l(new Date(a.lastUpdated))}else t(r),l(new Date)}catch(e){i("Failed to load cases"),console.error("Error loading cases:",e),t(r)}finally{n(!1)}};return{cases:e,loading:a,error:o,lastUpdated:d,updateCasesFromCsv:async e=>{try{n(!0),i(null);let a=await fetch(e);if(!a.ok)throw Error("Failed to fetch CSV: ".concat(a.status));let s=await a.text(),o=function(e){let t=e.trim().split("\n");if(t.length<2)return[];let a=t[0].split(",").map(e=>e.trim().toLowerCase()),s=[];for(let e=1;e<t.length;e++){let r=t[e].split(",").map(e=>e.trim());if(r.length<a.length)continue;let n={};if(a.forEach((e,t)=>{let a=r[t]||"";switch(e){case"date":let s=a.split("/");if(3===s.length){let[e,t,a]=s;n.date="".concat(a,"-").concat(t.padStart(2,"0"),"-").concat(e.padStart(2,"0"))}else n.date=a;break;case"case type":case"casetype":n.caseType=a;break;case"case number":case"casenumber":n.caseNumber=a;break;case"appellant":n.appellant=a;break;case"respondent":n.respondent=a;break;case"received":n.received=a;break;case"next date":case"nextdate":n.nextDate=a;break;case"status":case"custom_status":case"custom status":case"editable_status":n.status=a;break;case"taluka":n.taluka=a}}),n.date&&n.caseType&&n.caseNumber){let e={date:n.date,caseType:n.caseType,caseNumber:n.caseNumber,appellant:n.appellant||"Unknown",respondent:n.respondent||"Unknown",received:n.received||"प्राप्त",nextDate:n.nextDate||"2025-07-17",status:n.status||"",taluka:n.taluka||"Unknown",filedDate:n.date,lastUpdate:new Date().toISOString().split("T")[0]};s.push(e)}}return s}(s);return t(o.length>0?o:r),l(new Date),localStorage.setItem("legal-cases",JSON.stringify({cases:o.length>0?o:r,lastUpdated:new Date().toISOString()})),console.log("Successfully updated ".concat(o.length," cases from CSV")),{success:!0,count:o.length}}catch(t){let e=t instanceof Error?t.message:"Failed to update cases";return i(e),console.error("Error updating cases from CSV:",t),{success:!1,error:e}}finally{n(!1)}},refreshCases:()=>{localStorage.removeItem("legal-cases"),c()},addCase:a=>{try{let s=[...e,a];return t(s),l(new Date),localStorage.setItem("legal-cases",JSON.stringify({cases:s,lastUpdated:new Date().toISOString()})),console.log("Successfully added new case: ".concat(a.caseNumber)),{success:!0}}catch(t){let e=t instanceof Error?t.message:"Failed to add case";return i(e),console.error("Error adding case:",t),{success:!1,error:e}}}}}},8145:(e,t,a)=>{a.d(t,{E:()=>d});var s=a(5155);a(2115);var r=a(9708),n=a(2085),o=a(3999);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,asChild:n=!1,...d}=e,l=n?r.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:a}),t),...d})}},8482:(e,t,a)=>{a.d(t,{Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o});var s=a(5155);a(2115);var r=a(3999);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},8524:(e,t,a)=>{a.d(t,{A0:()=>o,BF:()=>i,Hj:()=>d,XI:()=>n,nA:()=>c,nd:()=>l});var s=a(5155);a(2115);var r=a(3999);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},9474:(e,t,a)=>{a.d(t,{T:()=>n});var s=a(5155);a(2115);var r=a(3999);function n(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9840:(e,t,a)=>{a.d(t,{Cf:()=>u,L3:()=>g,c7:()=>p,lG:()=>i,zM:()=>d});var s=a(5155);a(2115);var r=a(5452),n=a(4416),o=a(3999);function i(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,showCloseButton:i=!0,...d}=e;return(0,s.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,s.jsx)(c,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...d,children:[a,i&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a})}},9852:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(3999);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}}}]);