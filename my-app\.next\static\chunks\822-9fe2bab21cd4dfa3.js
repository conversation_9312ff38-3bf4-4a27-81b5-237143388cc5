"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[822],{1191:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-check",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]])},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2486:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2564:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>u});var r=n(2115),o=n(3655),i=n(5155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var u=a},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3081:(e,t,n)=>{n.d(t,{UC:()=>eN,In:()=>eA,q7:()=>eP,VF:()=>eO,p4:()=>eL,ZL:()=>ej,bL:()=>ek,wn:()=>e_,PP:()=>eI,l9:()=>eM,WT:()=>eT,LM:()=>eD});var r=n(2115),o=n(7650);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(5185),a=n(7328),u=n(6101),c=n(6081),s=n(4315),d=n(9178),f=n(2293),p=n(7900),v=n(1285),h=n(8795),m=n(4378),g=n(3655),y=n(9708),w=n(9033),x=n(5845),b=n(2712),E=n(2564),C=n(8168),R=n(3795),S=n(5155),k=[" ","Enter","ArrowUp","ArrowDown"],M=[" ","Enter"],T="Select",[A,j,N]=(0,a.N)(T),[D,P]=(0,c.A)(T,[N,h.Bk]),L=(0,h.Bk)(),[O,I]=D(T),[_,F]=D(T),B=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:g,form:y}=e,w=L(t),[b,E]=r.useState(null),[C,R]=r.useState(null),[k,M]=r.useState(!1),j=(0,s.jH)(d),[N,D]=(0,x.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:T}),[P,I]=(0,x.i)({prop:a,defaultProp:u,onChange:c,caller:T}),F=r.useRef(null),B=!b||y||!!b.closest("form"),[W,H]=r.useState(new Set),G=Array.from(W).map(e=>e.props.value).join(";");return(0,S.jsx)(h.bL,{...w,children:(0,S.jsxs)(O,{required:g,scope:t,trigger:b,onTriggerChange:E,valueNode:C,onValueNodeChange:R,valueNodeHasChildren:k,onValueNodeHasChildrenChange:M,contentId:(0,v.B)(),value:P,onValueChange:I,open:N,onOpenChange:D,dir:j,triggerPointerDownPosRef:F,disabled:m,children:[(0,S.jsx)(A.Provider,{scope:t,children:(0,S.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{H(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{H(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,S.jsxs)(eE,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>I(e.target.value),disabled:m,form:y,children:[void 0===P?(0,S.jsx)("option",{value:""}):null,Array.from(W)]},G):null]})})};B.displayName=T;var W="SelectTrigger",H=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=L(n),c=I(W,n),s=c.disabled||o,d=(0,u.s)(t,c.onTriggerChange),f=j(n),p=r.useRef("touch"),[v,m,y]=eR(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=eS(t,e,n);void 0!==r&&c.onValueChange(r.value)}),w=e=>{s||(c.onOpenChange(!0),y()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(h.Mz,{asChild:!0,...a,children:(0,S.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":eC(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(w(),e.preventDefault())})})})});H.displayName=W;var G="SelectValue",K=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=I(G,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.s)(t,c.onValueNodeChange);return(0,b.N)(()=>{s(d)},[s,d]),(0,S.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eC(c.value)?(0,S.jsx)(S.Fragment,{children:l}):i})});K.displayName=G;var U=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});U.displayName="SelectIcon";var V=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});V.displayName="SelectPortal";var z="SelectContent",q=r.forwardRef((e,t)=>{let n=I(z,e.__scopeSelect),[i,l]=r.useState();return((0,b.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,S.jsx)(Z,{...e,ref:t}):i?o.createPortal((0,S.jsx)(X,{scope:e.__scopeSelect,children:(0,S.jsx)(A.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),i):null});q.displayName=z;var[X,Y]=D(z),$=(0,y.TL)("SelectContent.RemoveScroll"),Z=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:E,...k}=e,M=I(z,n),[T,A]=r.useState(null),[N,D]=r.useState(null),P=(0,u.s)(t,e=>A(e)),[L,O]=r.useState(null),[_,F]=r.useState(null),B=j(n),[W,H]=r.useState(!1),G=r.useRef(!1);r.useEffect(()=>{if(T)return(0,C.Eq)(T)},[T]),(0,f.Oh)();let K=r.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[B,N]),U=r.useCallback(()=>K([L,T]),[K,L,T]);r.useEffect(()=>{W&&U()},[W,U]);let{onOpenChange:V,triggerPointerDownPosRef:q}=M;r.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=q.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=q.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,q]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[Y,Z]=eR(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eS(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==M.value&&M.value===t||r)&&(O(e),r&&(G.current=!0))},[M.value]),et=r.useCallback(()=>null==T?void 0:T.focus(),[T]),en=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==M.value&&M.value===t||r)&&F(e)},[M.value]),er="popper"===o?J:Q,eo=er===J?{side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:E}:{};return(0,S.jsx)(X,{scope:n,content:T,viewport:N,onViewportChange:D,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:U,selectedItemText:_,position:o,isPositioned:W,searchRef:Y,children:(0,S.jsx)(R.A,{as:$,allowPinchZoom:!0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:M.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null===(t=M.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:e=>e.preventDefault(),...k,...eo,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,l.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});Z.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=I(z,n),c=Y(z,n),[s,d]=r.useState(null),[f,p]=r.useState(null),v=(0,u.s)(t,e=>p(e)),h=j(n),m=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:E,focusSelectedItem:C}=c,R=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&w&&x&&E){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=E.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.right=d+"px"}let l=h(),u=window.innerHeight-20,c=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+v+c+parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),S=parseInt(C.paddingBottom,10),k=e.top+e.height/2-10,M=x.offsetHeight/2,T=p+v+(x.offsetTop+M);if(T<=k){let e=l.length>0&&x===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-k,M+(e?S:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);s.style.height=T+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;s.style.top="0px";let t=Math.max(k,p+w.offsetTop+(e?R:0)+M);s.style.height=t+(y-T)+"px",w.scrollTop=T-k+w.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=b+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>m.current=!0)}},[h,a.trigger,a.valueNode,s,f,w,x,E,a.dir,o]);(0,b.N)(()=>R(),[R]);let[k,M]=r.useState();(0,b.N)(()=>{f&&M(window.getComputedStyle(f).zIndex)},[f]);let T=r.useCallback(e=>{e&&!0===y.current&&(R(),null==C||C(),y.current=!1)},[R,C]);return(0,S.jsx)(ee,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,S.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,S.jsx)(g.sG.div,{...l,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Q.displayName="SelectItemAlignedPosition";var J=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=L(n);return(0,S.jsx)(h.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="SelectPopperPosition";var[ee,et]=D(z,{}),en="SelectViewport",er=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=Y(en,n),c=et(en,n),s=(0,u.s)(t,a.onViewportChange),d=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(A.Slot,{scope:n,children:(0,S.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});er.displayName=en;var eo="SelectGroup",[ei,el]=D(eo);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,v.B)();return(0,S.jsx)(ei,{scope:n,id:o,children:(0,S.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=eo;var ea="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=el(ea,n);return(0,S.jsx)(g.sG.div,{id:o.id,...r,ref:t})}).displayName=ea;var eu="SelectItem",[ec,es]=D(eu),ed=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=I(eu,n),d=Y(eu,n),f=s.value===o,[p,h]=r.useState(null!=a?a:""),[m,y]=r.useState(!1),w=(0,u.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),x=(0,v.B)(),b=r.useRef("touch"),E=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ec,{scope:n,value:o,disabled:i,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{h(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,S.jsx)(A.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,S.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:w,onFocus:(0,l.m)(c.onFocus,()=>y(!0)),onBlur:(0,l.m)(c.onBlur,()=>y(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(M.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});ed.displayName=eu;var ef="SelectItemText",ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=I(ef,n),s=Y(ef,n),d=es(ef,n),f=F(ef,n),[p,v]=r.useState(null),h=(0,u.s)(t,e=>v(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),m=null==p?void 0:p.textContent,y=r.useMemo(()=>(0,S.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(g.sG.span,{id:d.textId,...a,ref:h}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});ep.displayName=ef;var ev="SelectItemIndicator",eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return es(ev,n).isSelected?(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eh.displayName=ev;var em="SelectScrollUpButton",eg=r.forwardRef((e,t)=>{let n=Y(em,e.__scopeSelect),o=et(em,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(ex,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eg.displayName=em;var ey="SelectScrollDownButton",ew=r.forwardRef((e,t)=>{let n=Y(ey,e.__scopeSelect),o=et(ey,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(ex,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ew.displayName=ey;var ex=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=Y("SelectScrollButton",n),u=r.useRef(null),c=j(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,b.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{s()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eb="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=L(n),i=I(eb,n),l=Y(eb,n);return i.open&&"popper"===l.position?(0,S.jsx)(h.i3,{...o,...r,ref:t}):null}).displayName=eb;var eE=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...i}=e,l=r.useRef(null),a=(0,u.s)(t,l),c=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(o);return r.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[c,o]),(0,S.jsx)(g.sG.select,{...i,style:{...E.Qg,...i.style},ref:a,defaultValue:o})});function eC(e){return""===e||void 0===e}function eR(e){let t=(0,w.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function eS(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}eE.displayName="SelectBubbleInput";var ek=B,eM=H,eT=K,eA=U,ej=V,eN=q,eD=er,eP=ed,eL=ep,eO=eh,eI=eg,e_=ew},3453:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>V});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(2115)),a="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,l=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=o({async:!0,ssr:!1},e),l}(),v=function(){},h=l.forwardRef(function(e,t){var n,r,a,u,f=l.useRef(null),h=l.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=h[0],g=h[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,C=e.shards,R=e.sideCar,S=e.noRelative,k=e.noIsolation,M=e.inert,T=e.allowPinchZoom,A=e.as,j=e.gapMode,N=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,u=a.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),P=o(o({},N),m);return l.createElement(l.Fragment,null,E&&l.createElement(R,{sideCar:p,removeScrollBar:b,shards:C,noRelative:S,noIsolation:k,inert:M,setCallbacks:g,allowPinchZoom:!!T,lockRef:f,gapMode:j}),y?l.cloneElement(l.Children.only(w),o(o({},P),{ref:D})):l.createElement(void 0===A?"div":A,o({},P,{className:x,ref:D}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:a};var m=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,o({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=w(),S="data-scroll-locked",k=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},T=function(){l.useEffect(function(){return document.body.setAttribute(S,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=l.useMemo(function(){return C(o)},[o]);return l.createElement(R,{styles:k(i,!t,o,n?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){j=!1}var D=!!j&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=I(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&O(e,u)&&(f+=m,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},H=0,G=[];let K=(p.useMedium(function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(H++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=F(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=L(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?B(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,D),document.addEventListener("touchmove",c,D),document.addEventListener("touchstart",d,D),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,D),document.removeEventListener("touchmove",c,D),document.removeEventListener("touchstart",d,D)}},[]);var v=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?l.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),m);var U=l.forwardRef(function(e,t){return l.createElement(h,o({},e,{ref:t,sideCar:K}))});U.classNames=h.classNames;let V=U},3904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(2115);n(5155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=c||d&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5213:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>et,ZL:()=>J,bL:()=>Z,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>Q});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(1285),u=n(5845),c=n(9178),s=n(7900),d=n(4378),f=n(8905),p=n(3655),v=n(2293),h=n(3795),m=n(8168),g=n(9708),y=n(5155),w="Dialog",[x,b]=(0,l.A)(w),[E,C]=x(w),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:s,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};R.displayName=w;var S="DialogTrigger",k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(S,n),a=(0,i.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...r,ref:a,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});k.displayName=S;var M="DialogPortal",[T,A]=x(M,{forceMount:void 0}),j=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,l=C(M,t);return(0,y.jsx)(T,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||l.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};j.displayName=M;var N="DialogOverlay",D=r.forwardRef((e,t)=>{let n=A(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=C(N,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(L,{...o,ref:t})}):null});D.displayName=N;var P=(0,g.TL)("DialogOverlay.RemoveScroll"),L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(N,n);return(0,y.jsx)(h.A,{as:P,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":V(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),O="DialogContent",I=r.forwardRef((e,t)=>{let n=A(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=C(O,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(_,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});I.displayName=O;var _=r.forwardRef((e,t)=>{let n=C(O,e.__scopeDialog),l=r.useRef(null),a=(0,i.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=C(O,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...u}=e,d=C(O,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,v.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,y.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:d.titleId}),(0,y.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(W,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});H.displayName=W;var G="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(G,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=G;var K="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=C(K,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=K;var z="DialogTitleWarning",[q,X]=(0,l.q)(z,{contentName:O,titleName:W,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=X(z),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},Z=R,Q=k,J=j,ee=D,et=I,en=H,er=U},5488:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6932:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");return!function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}}(e,o,n),n}n.d(t,{N:()=>f});var l,a=n(2115),u=n(6081),c=n(6101),s=n(9708),d=n(5155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,u.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let f=e+"CollectionSlot",p=(0,s.TL)(f),v=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),l=(0,c.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:l,children:r})});v.displayName=f;let h=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,s.TL)(h),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,c.s)(t,l),s=i(h,n);return a.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,d.jsx)(g,{[m]:"",ref:u,children:r})});return y.displayName=h,[{Provider:l,Slot:v,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=h(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function h(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap},7550:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),l=n(9033),a=n(5155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,l.c)(m),E=(0,l.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>x(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:v(C.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||v(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,S.paused]),r.useEffect(()=>{if(w){h.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),h.remove(S)},0)}}},[w,b,E,S]);let k=r.useCallback(e=>{if(!n&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(i,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,S.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:k})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null===(n=(e=m(e,t))[0])||void 0===n||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},7924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),v=function(e){!(!e||f.has(e))&&(f.add(e),v(e.parentNode))};c.forEach(v);var h=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},8795:(e,t,n)=>{n.d(t,{Mz:()=>e3,i3:()=>tt,UC:()=>te,bL:()=>e8,Bk:()=>ez});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function v(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],C=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function k(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function M(e,t,n){let r,{reference:o,floating:i}=e,l=y(t),a=h(y(t)),u=m(a),c=p(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(v(t)){case"start":r[a]-=g*(n&&s?-1:1);break;case"end":r[a]+=g*(n&&s?-1:1)}return r}let T=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=M(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=M(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:v=0}=f(t,e),h=S(v),m=a[p?"floating"===d?"reference":"floating":d],g=k(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=k(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-b.top+h.top)/x.y,bottom:(b.bottom-g.bottom+h.bottom)/x.y,left:(g.left-b.left+h.left)/x.x,right:(b.right-g.right+h.right)/x.x}}function j(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function N(e){return o.some(t=>e[t]>=0)}let D=new Set(["left","top"]);async function P(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=v(n),u="y"===y(n),c=D.has(l)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:h,crossAxis:m,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof g&&(m="end"===a?-1*g:g),u?{x:m*s,y:h*c}:{x:h*c,y:m*s}}function L(){return"undefined"!=typeof window}function O(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function _(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!L()&&(e instanceof Node||e instanceof I(e).Node)}function B(e){return!!L()&&(e instanceof Element||e instanceof I(e).Element)}function W(e){return!!L()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function H(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let G=new Set(["inline","contents"]);function K(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!G.has(o)}let U=new Set(["table","td","th"]),V=[":popover-open",":modal"];function z(e){return V.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function $(e){let t=Z(),n=B(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(O(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||H(e)&&e.host||_(e);return H(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:W(n)&&K(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=I(o);if(i){let e=eo(l);return t.concat(l,l.visualViewport||[],K(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=W(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=a(n)!==i||a(r)!==l;return u&&(n=i,r=l),{width:n,height:r,$:u}}function el(e){return B(e)?e:e.contextElement}function ea(e){let t=el(e);if(!W(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),l=(i?a(n.width):n.width)/r,u=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let eu=c(0);function ec(e){let t=I(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=el(e),a=c(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(l))&&o)?ec(l):c(0),s=(i.left+u.x)/a.x,d=(i.top+u.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=I(l),t=r&&B(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=ea(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=l,o=eo(n=I(o))}}return k({width:f,height:p,x:s,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(_(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function ev(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=_(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=_(e),n=et(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:u}}(_(e));else if(B(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=W(e)?ea(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return k(r)}function eh(e){return"static"===ee(e).position}function em(e,t){if(!W(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return _(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(z(e))return r;if(!W(e)){let t=en(e);for(;t&&!J(t);){if(B(t)&&!eh(t))return t;t=en(t)}return r}let o=em(e,t);for(;o&&(n=o,U.has(O(n)))&&eh(o);)o=em(o,t);return o&&J(o)&&eh(o)&&!$(o)?r:o||function(e){let t=en(e);for(;W(t)&&!J(t);){if($(t))return t;if(z(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=W(t),o=_(t),i="fixed"===n,l=es(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i){if(("body"!==O(t)||K(o))&&(a=et(t)),r){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o))}i&&!r&&o&&(u.x=ed(o));let s=!o||r||i?c(0):ef(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=_(r),a=!!t&&z(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=W(r);if((f||!f&&!i)&&(("body"!==O(r)||K(l))&&(u=et(r)),W(r))){let e=es(r);s=ea(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?c(0):ef(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:_,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?z(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==O(e)),o=null,i="fixed"===ee(e).position,l=i?en(e):e;for(;B(l)&&!J(l);){let t=ee(l),n=$(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||K(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=ev(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ev(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=S(p),w={x:n,y:r},x=h(y(o)),b=m(x),E=await u.getDimensions(d),C="y"===x,R=C?"clientHeight":"clientWidth",k=a.reference[b]+a.reference[x]-w[x]-a.floating[b],M=w[x]-a.reference[x],T=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),A=T?T[R]:0;A&&await (null==u.isElement?void 0:u.isElement(T))||(A=c.floating[R]||a.floating[b]);let j=A/2-E[b]/2-1,N=i(g[C?"top":"left"],j),D=i(g[C?"bottom":"right"],j),P=A-E[b]-D,L=A/2-E[b]/2+(k/2-M/2),O=l(N,i(L,P)),I=!s.arrow&&null!=v(o)&&L!==O&&a.reference[b]/2-(L<N?N:D)-E[b]/2<0,_=I?L<N?L-N:L-P:0;return{[x]:w[x]+_,data:{[x]:O,centerOffset:L-O-_,...I&&{alignmentOffset:_}},reset:I}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return T(e,t,{...o,platform:i})};var eC=n(7650),eR="undefined"!=typeof document?r.useLayoutEffect:function(){};function eS(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eS(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eS(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ek(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eM(e,t){let n=ek(e);return Math.round(t*n)/n}function eT(e){let t=r.useRef(e);return eR(()=>{t.current=e}),t}let eA=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),ej=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await P(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},v=await A(t,s),m=y(p(o)),g=h(m),w=d[g],x=d[m];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+v[e],r=w-v[t];w=l(n,i(w,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=x+v[e],r=x-v[t];x=l(n,i(x,r))}let b=c.fn({...t,[g]:w,[m]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:a,[m]:u}}}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=y(o),v=h(d),m=s[v],g=s[d],w=f(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===v?"height":"width",t=i.reference[v]-i.floating[e]+x.mainAxis,n=i.reference[v]+i.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var b,E;let e="y"===v?"width":"height",t=D.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[v]:m,[d]:g}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:g}=t,{mainAxis:S=!0,crossAxis:k=!0,fallbackPlacements:M,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:j="none",flipAlignment:N=!0,...D}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let P=p(a),L=y(s),O=p(s)===s,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),_=M||(O||!N?[R(s)]:function(e){let t=R(e);return[w(e),t,w(t)]}(s)),F="none"!==j;!M&&F&&_.push(...function(e,t,n,r){let o=v(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:C;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(s,N,j,I));let B=[s,..._],W=await A(t,D),H=[],G=(null==(r=u.flip)?void 0:r.overflows)||[];if(S&&H.push(W[P]),k){let e=function(e,t,n){void 0===n&&(n=!1);let r=v(e),o=h(y(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=R(l)),[l,R(l)]}(a,c,I);H.push(W[e[0]],W[e[1]])}if(G=[...G,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==k||L===y(t)||G.every(e=>e.overflows[0]>0&&y(e.placement)===L)))return{data:{index:e,overflows:G},reset:{placement:t}};let n=null==(i=G.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(l=G.filter(e=>{if(F){let t=y(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a;let{placement:u,rects:c,platform:s,elements:d}=t,{apply:h=()=>{},...m}=f(e,t),g=await A(t,m),w=p(u),x=v(u),b="y"===y(u),{width:E,height:C}=c.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let R=C-g.top-g.bottom,S=E-g.left-g.right,k=i(C-g[o],R),M=i(E-g[a],S),T=!t.middlewareData.shift,j=k,N=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=R),T&&!x){let e=l(g.left,0),t=l(g.right,0),n=l(g.top,0),r=l(g.bottom,0);b?N=E-2*(0!==e||0!==t?e+t:l(g.left,g.right)):j=C-2*(0!==n||0!==r?n+r:l(g.top,g.bottom))}await h({...t,availableWidth:N,availableHeight:j});let D=await s.getDimensions(d.floating);return E!==D.width||C!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=j(await A(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:N(e)}}}case"escaped":{let e=j(await A(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:N(e)}}}default:return{}}}}}(e),options:[e,t]}),eI=(e,t)=>({...eA(e),options:[e,t]});var e_=n(3655),eF=n(5155),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eF.jsx)(e_.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var eW=n(6101),eH=n(6081),eG=n(9033),eK=n(2712),eU="Popper",[eV,ez]=(0,eH.A)(eU),[eq,eX]=eV(eU),eY=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eF.jsx)(eq,{scope:t,anchor:o,onAnchorChange:i,children:n})};eY.displayName=eU;var e$="PopperAnchor",eZ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eX(e$,n),a=r.useRef(null),u=(0,eW.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eF.jsx)(e_.sG.div,{...i,ref:u})});eZ.displayName=e$;var eQ="PopperContent",[eJ,e0]=eV(eQ),e1=r.forwardRef((e,t)=>{var n,o,a,c,s,d,f,p;let{__scopePopper:v,side:h="bottom",sideOffset:m=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:S="optimized",onPlaced:k,...M}=e,T=eX(eQ,v),[A,j]=r.useState(null),N=(0,eW.s)(t,e=>j(e)),[D,P]=r.useState(null),L=function(e){let[t,n]=r.useState(void 0);return(0,eK.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(D),O=null!==(f=null==L?void 0:L.width)&&void 0!==f?f:0,I=null!==(p=null==L?void 0:L.height)&&void 0!==p?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(b)?b:[b],W=B.length>0,H={padding:F,boundary:B.filter(e4),altBoundary:W},{refs:G,floatingStyles:K,placement:U,isPositioned:V,middlewareData:z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,v]=r.useState(o);eS(p,o)||v(o);let[h,m]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,m(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=l||h,E=a||g,C=r.useRef(null),R=r.useRef(null),S=r.useRef(d),k=null!=c,M=eT(c),T=eT(i),A=eT(s),j=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),eE(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};N.current&&!eS(S.current,t)&&(S.current=t,eC.flushSync(()=>{f(t)}))})},[p,t,n,T,A]);eR(()=>{!1===s&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let N=r.useRef(!1);eR(()=>(N.current=!0,()=>{N.current=!1}),[]),eR(()=>{if(b&&(C.current=b),E&&(R.current=E),b&&E){if(M.current)return M.current(b,E,j);j()}},[b,E,j,M,k]);let D=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:x}),[w,x]),P=r.useMemo(()=>({reference:b,floating:E}),[b,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!P.floating)return e;let t=eM(P.floating,d.x),r=eM(P.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ek(P.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,P.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:j,refs:D,elements:P,floatingStyles:L}),[d,j,D,P,L])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=el(e),v=a||c?[...p?er(p):[],...er(t)]:[];v.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,o=_(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:v,width:h,height:m}=f;if(s||t(),!h||!m)return;let g=u(v),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(v+m))+"px "+-u(p)+"px",threshold:l(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ex(f,e.getBoundingClientRect())||c(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,m=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?es(e):null;return f&&function t(){let r=es(e);y&&!ex(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;v.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===S})},elements:{reference:T.anchor},middleware:[ej({mainAxis:m+I,alignmentAxis:y}),x&&eN({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?eD():void 0,...H}),x&&eP({...H}),eL({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&eI({element:D,padding:w}),e6({arrowWidth:O,arrowHeight:I}),R&&eO({strategy:"referenceHidden",...H})]}),[q,X]=e7(U),Y=(0,eG.c)(k);(0,eK.N)(()=>{V&&(null==Y||Y())},[V,Y]);let $=null===(n=z.arrow)||void 0===n?void 0:n.x,Z=null===(o=z.arrow)||void 0===o?void 0:o.y,Q=(null===(a=z.arrow)||void 0===a?void 0:a.centerOffset)!==0,[J,ee]=r.useState();return(0,eK.N)(()=>{A&&ee(window.getComputedStyle(A).zIndex)},[A]),(0,eF.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:V?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:J,"--radix-popper-transform-origin":[null===(c=z.transformOrigin)||void 0===c?void 0:c.x,null===(s=z.transformOrigin)||void 0===s?void 0:s.y].join(" "),...(null===(d=z.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(eJ,{scope:v,placedSide:q,onArrowChange:P,arrowX:$,arrowY:Z,shouldHideArrow:Q,children:(0,eF.jsx)(e_.sG.div,{"data-side":q,"data-align":X,...M,ref:N,style:{...M.style,animation:V?void 0:"none"}})})})});e1.displayName=eQ;var e5="PopperArrow",e2={top:"bottom",right:"left",bottom:"top",left:"right"},e9=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e0(e5,n),i=e2[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e4(e){return null!==e}e9.displayName=e5;var e6=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=e7(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function e7(e){let[t,n="center"]=e.split("-");return[t,n]}var e8=eY,e3=eZ,te=e1,tt=e9},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),o=n(6101),i=n(2712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.s)(l.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),u=n(9033),c=n(5155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=o.useContext(d),[C,R]=o.useState(null),S=null!==(f=null==C?void 0:C.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,k]=o.useState({}),M=(0,a.s)(t,e=>R(e)),T=Array.from(E.layers),[A]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),j=T.indexOf(A),N=C?T.indexOf(C):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,P=N>=j,L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){v("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!P||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},S),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},S);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},S),o.useEffect(()=>{if(C)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[C,S,h,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,E]),o.useEffect(()=>{let e=()=>k({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...b,ref:M,style:{pointerEvents:D?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9449:(e,t,n)=>{n.d(t,{UC:()=>e7,q7:()=>e8,ZL:()=>e6,bL:()=>e9,l9:()=>e4});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(5845),u=n(3655),c=n(7328),s=n(4315),d=n(9178),f=n(2293),p=n(7900),v=n(1285),h=n(8795),m=n(4378),g=n(8905),y=n(9033),w=n(5155),x="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[C,R,S]=(0,c.N)(E),[k,M]=(0,l.A)(E,[S]),[T,A]=k(E),j=r.forwardRef((e,t)=>(0,w.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(N,{...e,ref:t})})}));j.displayName=E;var N=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:l,loop:c=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:v,onEntryFocus:h,preventScrollOnEntryFocus:m=!1,...g}=e,C=r.useRef(null),S=(0,i.s)(t,C),k=(0,s.jH)(d),[M,A]=(0,a.i)({prop:f,defaultProp:null!=p?p:null,onChange:v,caller:E}),[j,N]=r.useState(!1),D=(0,y.c)(h),P=R(n),L=r.useRef(!1),[I,_]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(x,D),()=>e.removeEventListener(x,D)},[D]),(0,w.jsx)(T,{scope:n,orientation:l,dir:k,loop:c,currentTabStopId:M,onItemFocus:r.useCallback(e=>A(e),[A]),onItemShiftTab:r.useCallback(()=>N(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,w.jsx)(u.sG.div,{tabIndex:j||0===I?-1:0,"data-orientation":l,...g,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(x,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),m)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),D="RovingFocusGroupItem",P=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:l=!1,tabStopId:a,children:c,...s}=e,d=(0,v.B)(),f=a||d,p=A(D,n),h=p.currentTabStopId===f,m=R(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:x}=p;return r.useEffect(()=>{if(i)return g(),()=>y()},[i,g,y]),(0,w.jsx)(C.ItemSlot,{scope:n,id:f,focusable:i,active:l,children:(0,w.jsx)(u.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>O(n))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=x}):c})})});P.displayName=D;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=n(9708),_=n(8168),F=n(3795),B=["Enter"," "],W=["ArrowUp","PageDown","End"],H=["ArrowDown","PageUp","Home",...W],G={ltr:[...B,"ArrowRight"],rtl:[...B,"ArrowLeft"]},K={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[V,z,q]=(0,c.N)(U),[X,Y]=(0,l.A)(U,[q,h.Bk,M]),$=(0,h.Bk)(),Z=M(),[Q,J]=X(U),[ee,et]=X(U),en=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:l,modal:a=!0}=e,u=$(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,y.c)(l),v=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(h.bL,{...u,children:(0,w.jsx)(Q,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,w.jsx)(ee,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:a,children:o})})})};en.displayName=U;var er=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=$(n);return(0,w.jsx)(h.Mz,{...o,...r,ref:t})});er.displayName="MenuAnchor";var eo="MenuPortal",[ei,el]=X(eo,{forceMount:void 0}),ea=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=J(eo,t);return(0,w.jsx)(ei,{scope:t,forceMount:n,children:(0,w.jsx)(g.C,{present:n||i.open,children:(0,w.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};ea.displayName=eo;var eu="MenuContent",[ec,es]=X(eu),ed=r.forwardRef((e,t)=>{let n=el(eu,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=J(eu,e.__scopeMenu),l=et(eu,e.__scopeMenu);return(0,w.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:r||i.open,children:(0,w.jsx)(V.Slot,{scope:e.__scopeMenu,children:l.modal?(0,w.jsx)(ef,{...o,ref:t}):(0,w.jsx)(ep,{...o,ref:t})})})})}),ef=r.forwardRef((e,t)=>{let n=J(eu,e.__scopeMenu),l=r.useRef(null),a=(0,i.s)(t,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,_.Eq)(e)},[]),(0,w.jsx)(eh,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ep=r.forwardRef((e,t)=>{let n=J(eu,e.__scopeMenu);return(0,w.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ev=(0,I.TL)("MenuContent.ScrollLock"),eh=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:a,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:E,...C}=e,R=J(eu,n),S=et(eu,n),k=$(n),M=Z(n),T=z(n),[A,N]=r.useState(null),D=r.useRef(null),P=(0,i.s)(t,D,R.onContentChange),L=r.useRef(0),O=r.useRef(""),I=r.useRef(0),_=r.useRef(null),B=r.useRef("right"),G=r.useRef(0),K=E?F.A:r.Fragment,U=e=>{var t,n;let r=O.current+e,o=T().filter(e=>!e.disabled),i=document.activeElement,l=null===(t=o.find(e=>e.ref.current===i))||void 0===t?void 0:t.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,l),u=null===(n=o.find(e=>e.textValue===a))||void 0===n?void 0:n.ref.current;!function e(t){O.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};r.useEffect(()=>()=>window.clearTimeout(L.current),[]),(0,f.Oh)();let V=r.useCallback(e=>{var t,n;return B.current===(null===(t=_.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,c=l.y,s=a.x,d=a.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(n=_.current)||void 0===n?void 0:n.area)},[]);return(0,w.jsx)(ec,{scope:n,searchRef:O,onItemEnter:r.useCallback(e=>{V(e)&&e.preventDefault()},[V]),onItemLeave:r.useCallback(e=>{var t;V(e)||(null===(t=D.current)||void 0===t||t.focus(),N(null))},[V]),onTriggerLeave:r.useCallback(e=>{V(e)&&e.preventDefault()},[V]),pointerGraceTimerRef:I,onPointerGraceIntentChange:r.useCallback(e=>{_.current=e},[]),children:(0,w.jsx)(K,{...E?{as:ev,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(p.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null===(t=D.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,w.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,w.jsx)(j,{asChild:!0,...M,dir:S.dir,orientation:"vertical",loop:l,currentTabStopId:A,onCurrentTabStopIdChange:N,onEntryFocus:(0,o.m)(v,e=>{S.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eH(R.open),"data-radix-menu-content":"",dir:S.dir,...k,...C,ref:P,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&U(e.key));let o=D.current;if(e.target!==o||!H.includes(e.key))return;e.preventDefault();let i=T().filter(e=>!e.disabled).map(e=>e.ref.current);W.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eU(e=>{let t=e.target,n=G.current!==e.clientX;e.currentTarget.contains(t)&&n&&(B.current=e.clientX>G.current?"right":"left",G.current=e.clientX)}))})})})})})})});ed.displayName=eu;var em=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"group",...r,ref:t})});em.displayName="MenuGroup";var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{...r,ref:t})});eg.displayName="MenuLabel";var ey="MenuItem",ew="menu.itemSelect",ex=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:l,...a}=e,c=r.useRef(null),s=et(ey,e.__scopeMenu),d=es(ey,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,w.jsx)(eb,{...a,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>null==l?void 0:l(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&B.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:l=!1,textValue:a,...c}=e,s=es(ey,n),d=Z(n),f=r.useRef(null),p=(0,i.s)(t,f),[v,h]=r.useState(!1),[m,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;g((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[c.children]),(0,w.jsx)(V.ItemSlot,{scope:n,disabled:l,textValue:null!=a?a:m,children:(0,w.jsx)(P,{asChild:!0,...d,focusable:!l,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eU(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eU(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eE=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,w.jsx)(ej,{scope:e.__scopeMenu,checked:n,children:(0,w.jsx)(ex,{role:"menuitemcheckbox","aria-checked":eG(n)?"mixed":n,...i,ref:t,"data-state":eK(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!eG(n)||!n),{checkForDefaultPrevented:!1})})})});eE.displayName="MenuCheckboxItem";var eC="MenuRadioGroup",[eR,eS]=X(eC,{value:void 0,onValueChange:()=>{}}),ek=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,y.c)(r);return(0,w.jsx)(eR,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,w.jsx)(em,{...o,ref:t})})});ek.displayName=eC;var eM="MenuRadioItem",eT=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=eS(eM,e.__scopeMenu),l=n===i.value;return(0,w.jsx)(ej,{scope:e.__scopeMenu,checked:l,children:(0,w.jsx)(ex,{role:"menuitemradio","aria-checked":l,...r,ref:t,"data-state":eK(l),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null===(e=i.onValueChange)||void 0===e?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});eT.displayName=eM;var eA="MenuItemIndicator",[ej,eN]=X(eA,{checked:!1}),eD=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eN(eA,n);return(0,w.jsx)(g.C,{present:r||eG(i.checked)||!0===i.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":eK(i.checked)})})});eD.displayName=eA;var eP=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eP.displayName="MenuSeparator";var eL=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=$(n);return(0,w.jsx)(h.i3,{...o,...r,ref:t})});eL.displayName="MenuArrow";var[eO,eI]=X("MenuSub"),e_="MenuSubTrigger",eF=r.forwardRef((e,t)=>{let n=J(e_,e.__scopeMenu),l=et(e_,e.__scopeMenu),a=eI(e_,e.__scopeMenu),u=es(e_,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,w.jsx)(er,{asChild:!0,...f,children:(0,w.jsx)(eb,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eH(n.open),...e,ref:(0,i.t)(t,a.onTriggerChange),onClick:t=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eU(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eU(e=>{var t,r;p();let o=null===(t=n.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(r=n.content)||void 0===r?void 0:r.dataset.side,i="right"===t,l=o[i?"left":"right"],a=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:l,y:o.top},{x:a,y:o.top},{x:a,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&G[l.dir].includes(t.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eF.displayName=e_;var eB="MenuSubContent",eW=r.forwardRef((e,t)=>{let n=el(eu,e.__scopeMenu),{forceMount:l=n.forceMount,...a}=e,u=J(eu,e.__scopeMenu),c=et(eu,e.__scopeMenu),s=eI(eB,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,w.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:l||u.open,children:(0,w.jsx)(V.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eh,{id:s.contentId,"aria-labelledby":s.triggerId,...a,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=K[c.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null===(r=s.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function eH(e){return e?"open":"closed"}function eG(e){return"indeterminate"===e}function eK(e){return eG(e)?"indeterminate":e?"checked":"unchecked"}function eU(e){return t=>"mouse"===t.pointerType?e(t):void 0}eW.displayName=eB;var eV="DropdownMenu",[ez,eq]=(0,l.A)(eV,[Y]),eX=Y(),[eY,e$]=ez(eV),eZ=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:l,onOpenChange:u,modal:c=!0}=e,s=eX(t),d=r.useRef(null),[f,p]=(0,a.i)({prop:i,defaultProp:null!=l&&l,onChange:u,caller:eV});return(0,w.jsx)(eY,{scope:t,triggerId:(0,v.B)(),triggerRef:d,contentId:(0,v.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,w.jsx)(en,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eZ.displayName=eV;var eQ="DropdownMenuTrigger",eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...l}=e,a=e$(eQ,n),c=eX(n);return(0,w.jsx)(er,{asChild:!0,...c,children:(0,w.jsx)(u.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...l,ref:(0,i.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eJ.displayName=eQ;var e0=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eX(t);return(0,w.jsx)(ea,{...r,...n})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e5=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,l=e$(e1,n),a=eX(n),u=r.useRef(!1);return(0,w.jsx)(ed,{id:l.contentId,"aria-labelledby":l.triggerId,...a,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null===(t=l.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!l.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e1,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(em,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eg,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var e2=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(ex,{...o,...r,ref:t})});e2.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eE,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(ek,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eT,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eD,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eP,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eL,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eF,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eW,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e9=eZ,e4=eJ,e6=e0,e7=e5,e8=e2},9613:(e,t,n)=>{n.d(t,{Kq:()=>I});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),u=(n(1285),n(8795)),c=(n(4378),n(8905)),s=n(3655),d=n(9708),f=(n(5845),n(2564)),p=n(5155),[v,h]=(0,l.A)("Tooltip",[u.Bk]),m=(0,u.Bk)(),g="TooltipProvider",y="tooltip.open",[w,x]=v(g),b=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=r.useRef(!0),u=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,p.jsx)(w,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};b.displayName=g;var E="Tooltip",[C,R]=v(E),S="TooltipTrigger";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=R(S,n),c=x(S,n),d=m(n),f=r.useRef(null),v=(0,i.s)(t,f,a.onTriggerChange),h=r.useRef(!1),g=r.useRef(!1),y=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,p.jsx)(u.Mz,{asChild:!0,...d,children:(0,p.jsx)(s.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:v,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||g.current||c.isPointerInTransitRef.current||(a.onTriggerEnter(),g.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),g.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})}).displayName=S;var[k,M]=v("TooltipPortal",{forceMount:void 0}),T="TooltipContent",A=r.forwardRef((e,t)=>{let n=M(T,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=R(T,e.__scopeTooltip);return(0,p.jsx)(c.C,{present:r||l.open,children:l.disableHoverableContent?(0,p.jsx)(L,{side:o,...i,ref:t}):(0,p.jsx)(j,{side:o,...i,ref:t})})}),j=r.forwardRef((e,t)=>{let n=R(T,e.__scopeTooltip),o=x(T,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[u,c]=r.useState(null),{trigger:s,onClose:d}=n,f=l.current,{onPointerInTransitChange:v}=o,h=r.useCallback(()=>{c(null),v(!1)},[v]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),v(!0)},[v]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(s&&f){let e=e=>m(e,f),t=e=>m(e,s);return s.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{s.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[s,f,m,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==s?void 0:s.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,c=l.y,s=a.x,d=a.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}(n,u);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[s,f,u,d,h]),(0,p.jsx)(L,{...e,ref:a})}),[N,D]=v(E,{isInside:!1}),P=(0,d.Dc)("TooltipContent"),L=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:c,...s}=e,d=R(T,n),v=m(n),{onClose:h}=d;return r.useEffect(()=>(document.addEventListener(y,h),()=>document.removeEventListener(y,h)),[h]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,p.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,p.jsxs)(u.UC,{"data-state":d.stateAttribute,...v,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,p.jsx)(P,{children:o}),(0,p.jsx)(N,{scope:n,isInside:!0,children:(0,p.jsx)(f.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});A.displayName=T;var O="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=m(n);return D(O,n).isInside?null:(0,p.jsx)(u.i3,{...o,...r,ref:t})}).displayName=O;var I=b}}]);