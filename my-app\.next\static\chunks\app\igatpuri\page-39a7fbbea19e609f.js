(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[804],{3947:(e,s,a)=>{Promise.resolve().then(a.bind(a,5794))},5794:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>I});var l=a(5155),r=a(2115),t=a(3453),n=a(1191),c=a(9074),i=a(4186),d=a(2486),o=a(3904),m=a(7550),x=a(8686),h=a(4516),u=a(7924),p=a(6932),g=a(1788),j=a(5213),b=a(5488),N=a(7168),v=a(9852),f=a(8482),w=a(8524),C=a(5784),y=a(8145),S=a(7777),A=a(7133),k=a(9840),T=a(9474),D=a(1886),L=a(7996);function E(){let{cases:e,loading:s,error:a,lastUpdated:E,updateCasesFromCsv:I,refreshCases:$,addCase:O}=(0,L.L)(),[R,F]=(0,r.useState)(""),[M,_]=(0,r.useState)("All Types"),[V,B]=(0,r.useState)("All Statuses"),[q,z]=(0,r.useState)(1),[P,U]=(0,r.useState)(null),[W,Z]=(0,r.useState)("asc"),[Y,H]=(0,r.useState)(10),[G,Q]=(0,r.useState)({}),[X,J]=(0,r.useState)({}),[K,ee]=(0,r.useState)({}),[es,ea]=(0,r.useState)({}),[el,er]=(0,r.useState)(""),[et,en]=(0,r.useState)(null),[ec,ei]=(0,r.useState)(!1),[ed,eo]=(0,r.useState)({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),em=(0,r.useMemo)(()=>{console.log("Total cases received:",e.length),console.log("Cases data:",e);let s=e.filter(e=>(console.log('Case taluka: "'.concat(e.taluka,'", matches Igatpuri:'),"Igatpuri"===e.taluka),"Igatpuri"===e.taluka));return console.log("Filtered Igatpuri cases:",s.length),s},[e]),ex=(0,r.useMemo)(()=>["All Types",...new Set(em.map(e=>e.caseType).filter(Boolean))],[em]),eh=(0,r.useMemo)(()=>["All Statuses",...new Set(em.map(e=>X[e.caseNumber]||e.received).filter(Boolean))],[em,X]),eu=new Date,ep=(0,r.useMemo)(()=>{let e=em.filter(e=>{var s,a,l,r;let t=(null===(s=e.caseNumber)||void 0===s?void 0:s.toLowerCase().includes(R.toLowerCase()))||(null===(a=e.appellant)||void 0===a?void 0:a.toLowerCase().includes(R.toLowerCase()))||(null===(l=e.respondent)||void 0===l?void 0:l.toLowerCase().includes(R.toLowerCase()))||(null===(r=e.caseType)||void 0===r?void 0:r.toLowerCase().includes(R.toLowerCase())),n="All Types"===M||e.caseType===M,c="All Statuses"===V||(X[e.caseNumber]||e.received)===V;return t&&n&&c});return P&&e.sort((e,s)=>{let a=e[P],l=s[P];return("date"===P&&(a=new Date(a).getTime(),l=new Date(l).getTime()),a<l)?"asc"===W?-1:1:a>l?"asc"===W?1:-1:0}),e},[em,R,M,V,P,W]),eg=Math.ceil(ep.length/Y),ej=(q-1)*Y,eb=ep.slice(ej,ej+Y),eN=(0,r.useMemo)(()=>{let e=ep.length;return{total:e,received:ep.filter(e=>"प्राप्त"===(X[e.caseNumber]||e.received)).length,nextDate:ep.filter(e=>""!==(K[e.caseNumber]||e.nextDate||"").trim()).length}},[ep,X,K]),ev=e=>{P===e?Z("asc"===W?"desc":"asc"):(U(e),Z("asc")),z(1)},ef=e=>{z(e)},ew=async(e,s)=>{Q(a=>({...a,[e]:s}));try{let a=await (0,D.Yp)(e,"status",s);a.success||console.error("Failed to update status:",a.error)}catch(e){console.error("Error updating status:",e)}},eC=async(e,s)=>{J(a=>({...a,[e]:s}));try{let a=await (0,D.Yp)(e,"received",s);a.success||console.error("Failed to update received status:",a.error)}catch(e){console.error("Error updating received status:",e)}},ey=async(e,s)=>{ee(a=>({...a,[e]:s}));try{let a=await (0,D.Yp)(e,"next_date",s);a.success||console.error("Failed to update next date:",a.error)}catch(e){console.error("Error updating next date:",e)}},eS=(e,s)=>{en(e),er(G[e]||s||""),ea(s=>({...s,[e]:!0}))},eA=e=>{ea(s=>({...s,[e]:!1})),en(null),er("")},ek=e=>{ew(e,el),eA(e)},eT=e=>{let s=ep.map(e=>({Date:e.date,"Case Type":e.caseType,"Case Number":e.caseNumber,Year:e.year,Appellant:e.appellant,Respondent:e.respondent,Received:X[e.caseNumber]||e.received||"-",Status:G[e.caseNumber]||e.status||""}));if("CSV"===e){let e=new Blob([[Object.keys(s[0]).join(","),...s.map(e=>Object.values(e).join(","))].join("\n")],{type:"text/csv"}),a=window.URL.createObjectURL(e),l=document.createElement("a");l.href=a,l.download="igatpuri-cases-".concat(new Date().toISOString().split("T")[0],".csv"),l.click(),window.URL.revokeObjectURL(a)}else alert("".concat(e," export functionality would be implemented here"))},eD=e=>{let s=(null==e?void 0:e.toLowerCase())||"";if(s.includes("completed"))return{variant:"default",icon:t.A,color:"text-green-700"};if(s.includes("received"))return{variant:"secondary",icon:n.A,color:"text-orange-700"};if(s.includes("scheduled"))return{variant:"outline",icon:c.A,color:"text-amber-700"};if(s.includes("review"))return{variant:"secondary",icon:i.A,color:"text-yellow-700"};if(s.includes("issued"))return{variant:"outline",icon:d.A,color:"text-red-700"};else return{variant:"outline",icon:i.A,color:"text-gray-700"}};return s?(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50/20 to-amber-50/20 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-orange-600"}),(0,l.jsx)("p",{className:"text-orange-700",children:"Loading cases..."})]})}):(0,l.jsxs)(S.Bc,{children:[(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50/20 to-amber-50/20",children:(0,l.jsxs)("div",{className:"mx-auto max-w-7xl space-y-4 p-4",children:[(0,l.jsxs)("div",{className:"text-center space-y-2 pb-4 border-b bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm border-orange-100",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,l.jsxs)(N.$,{variant:"ghost",size:"sm",onClick:()=>window.location.href="/",className:"flex items-center gap-2 text-orange-700 hover:text-orange-900 hover:bg-orange-100 self-start sm:self-center",children:[(0,l.jsx)(m.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Back to Home"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg",children:(0,l.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,l.jsxs)("div",{className:"text-center sm:text-left",children:[(0,l.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-orange-900",children:"Igatpuri Legal Case Dashboard"}),(0,l.jsx)("p",{className:"text-xs sm:text-sm text-orange-700",children:"Sub-Divisional Magistrate Office, Nashik"})]})]}),(0,l.jsx)("div",{className:"hidden sm:block w-24"})," "]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-6 text-xs text-orange-600",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(h.A,{className:"h-3 w-3"}),(0,l.jsx)("span",{children:"Igatpuri Subdivision"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(c.A,{className:"h-3 w-3"}),(0,l.jsx)("span",{className:"text-center",children:eu.toLocaleDateString("en-IN",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4",children:[(0,l.jsx)(f.Zp,{className:"border border-orange-100 shadow-sm bg-gradient-to-br from-orange-50/50 to-white",children:(0,l.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,l.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-orange-600 mb-1",children:eN.total}),(0,l.jsx)("div",{className:"text-xs text-orange-700",children:"Total Cases"})]})}),(0,l.jsx)(f.Zp,{className:"border border-green-100 shadow-sm bg-gradient-to-br from-green-50/50 to-white",children:(0,l.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,l.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600 mb-1",children:eN.received}),(0,l.jsx)("div",{className:"text-xs text-green-700",children:"Received"})]})}),(0,l.jsx)(f.Zp,{className:"border border-blue-100 shadow-sm bg-gradient-to-br from-blue-50/50 to-white",children:(0,l.jsxs)(f.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,l.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-blue-600 mb-1",children:eN.nextDate}),(0,l.jsx)("div",{className:"text-xs text-blue-700",children:"Next Date"})]})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(f.Zp,{className:"border border-orange-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,l.jsxs)(f.Wu,{className:"p-4 space-y-4",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-400 h-4 w-4"}),(0,l.jsx)(v.p,{placeholder:"Search by case number, appellant, respondent, or type...",value:R,onChange:e=>F(e.target.value),className:"pl-10 h-10 border-orange-200 focus:border-orange-500 bg-white/50"})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-wrap",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,l.jsxs)(C.l6,{value:M,onValueChange:_,children:[(0,l.jsx)(C.bq,{className:"h-9 text-sm border-orange-200 bg-white/50 min-w-[120px]",children:(0,l.jsx)(C.yv,{placeholder:"Type"})}),(0,l.jsx)(C.gC,{children:ex.map(e=>(0,l.jsx)(C.eb,{value:e,children:e},e))})]}),(0,l.jsxs)(C.l6,{value:V,onValueChange:B,children:[(0,l.jsx)(C.bq,{className:"h-9 text-sm border-orange-200 bg-white/50 min-w-[120px]",children:(0,l.jsx)(C.yv,{placeholder:"Status"})}),(0,l.jsx)(C.gC,{children:eh.map(e=>(0,l.jsx)(C.eb,{value:e,children:e},e))})]})]}),(0,l.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,l.jsxs)(N.$,{onClick:()=>ei(!0),className:"h-9 text-sm bg-orange-600 hover:bg-orange-700 text-white",children:[(0,l.jsx)("span",{className:"text-lg mr-1",children:"+"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Add New Case"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Add"})]}),(0,l.jsxs)(N.$,{variant:"outline",onClick:()=>{F(""),_("All Types"),B("All Statuses"),z(1),U(null)},className:"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50",children:[(0,l.jsx)(p.A,{className:"h-3 w-3 mr-1"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Clear"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Clear"})]}),(0,l.jsxs)(A.rI,{children:[(0,l.jsx)(A.ty,{asChild:!0,children:(0,l.jsxs)(N.$,{variant:"outline",className:"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50",children:[(0,l.jsx)(g.A,{className:"h-3 w-3 mr-1"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Export"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Export"})]})}),(0,l.jsxs)(A.SQ,{children:[(0,l.jsx)(A._2,{onClick:()=>eT("CSV"),children:"Export as CSV"}),(0,l.jsx)(A._2,{onClick:()=>eT("PDF"),children:"Export as PDF"}),(0,l.jsx)(A._2,{onClick:()=>eT("Excel"),children:"Export as Excel"})]})]}),(0,l.jsxs)(N.$,{variant:"outline",onClick:$,disabled:s,className:"h-9 text-sm bg-white/50 border-orange-200 hover:bg-orange-50",children:[(0,l.jsx)(o.A,{className:"h-3 w-3 mr-1 ".concat(s?"animate-spin":"")}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Refresh"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Refresh"})]})]})]}),(0,l.jsx)("div",{className:"flex items-center justify-between text-sm text-orange-700",children:(0,l.jsxs)("span",{children:["Showing ",eb.length," of ",ep.length," cases"]})})]})}),(0,l.jsx)(f.Zp,{className:"border border-orange-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,l.jsxs)(f.Wu,{className:"p-0",children:[(0,l.jsx)("div",{className:"overflow-x-auto min-w-full",children:(0,l.jsxs)(w.XI,{className:"min-w-[800px]",children:[(0,l.jsx)(w.A0,{children:(0,l.jsxs)(w.Hj,{className:"border-b border-orange-100",children:[(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[100px]",children:"Case Type"}),(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[140px]",children:(0,l.jsxs)(N.$,{variant:"ghost",onClick:()=>ev("caseNumber"),className:"h-auto p-0 font-semibold hover:bg-orange-50",children:["Case Number","caseNumber"===P&&("asc"===W?(0,l.jsx)(j.A,{className:"ml-1 h-3 w-3"}):(0,l.jsx)(b.A,{className:"ml-1 h-3 w-3"}))]})}),(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[120px]",children:"Appellant"}),(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[120px]",children:"Respondent"}),(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[80px]",children:"Received"}),(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[140px]",children:"Next Date"}),(0,l.jsx)(w.nd,{className:"font-semibold text-orange-900 min-w-[120px]",children:"Status"})]})}),(0,l.jsx)(w.BF,{children:eb.map((e,s)=>(eD(e.status).icon,(0,l.jsxs)(w.Hj,{className:"hover:bg-orange-50/50",children:[(0,l.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)(y.E,{variant:"outline",className:"text-xs border-orange-200 text-orange-700 whitespace-nowrap",children:e.caseType})}),(0,l.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)("div",{className:"font-semibold text-sm break-all",children:e.caseNumber})}),(0,l.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)("div",{className:"font-medium text-sm break-words",children:e.appellant})}),(0,l.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)("div",{className:"text-sm break-words",children:e.respondent})}),(0,l.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,l.jsxs)(C.l6,{value:X[e.caseNumber]||e.received||"-",onValueChange:s=>eC(e.caseNumber,s),children:[(0,l.jsx)(C.bq,{className:"w-full min-w-[70px] h-8 text-xs border-orange-200",children:(0,l.jsx)(C.yv,{})}),(0,l.jsxs)(C.gC,{children:[(0,l.jsx)(C.eb,{value:"प्राप्त",children:"प्राप्त"}),(0,l.jsx)(C.eb,{value:"-",children:"-"})]})]})}),(0,l.jsx)(w.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)(v.p,{type:"date",value:K[e.caseNumber]||e.nextDate||"2025-07-17",onChange:s=>ey(e.caseNumber,s.target.value),className:"w-full min-w-[130px] h-8 text-xs border-orange-200 focus:border-orange-400"})}),(0,l.jsx)(w.nA,{children:(0,l.jsxs)(k.lG,{open:es[e.caseNumber]||!1,onOpenChange:s=>{s||eA(e.caseNumber)},children:[(0,l.jsx)(k.zM,{asChild:!0,children:(0,l.jsxs)(N.$,{variant:"outline",size:"sm",className:"text-xs h-8 min-w-[120px] justify-start",onClick:()=>eS(e.caseNumber,e.status),children:[(G[e.caseNumber]||e.status||"Enter status...").substring(0,15),(G[e.caseNumber]||e.status||"").length>15?"...":""]})}),(0,l.jsxs)(k.Cf,{className:"sm:max-w-[425px]",children:[(0,l.jsx)(k.c7,{children:(0,l.jsxs)(k.L3,{children:["Edit Status - Case #",e.caseNumber]})}),(0,l.jsx)("div",{className:"grid gap-4 py-4",children:(0,l.jsx)(T.T,{value:el,onChange:e=>er(e.target.value),placeholder:"Enter detailed status information...",className:"min-h-[100px]"})}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(N.$,{variant:"outline",onClick:()=>eA(e.caseNumber),children:"Cancel"}),(0,l.jsx)(N.$,{onClick:()=>ek(e.caseNumber),children:"Save"})]})]})]})})]},"".concat(e.caseNumber,"-").concat(s))))})]})}),eg>1&&(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-3 p-4 border-t border-orange-100",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)("span",{className:"text-sm text-orange-700",children:["Page ",q," of ",eg]}),(0,l.jsxs)(C.l6,{value:Y.toString(),onValueChange:e=>{H(Number(e)),z(1)},children:[(0,l.jsx)(C.bq,{className:"w-20 h-8 border-orange-200",children:(0,l.jsx)(C.yv,{})}),(0,l.jsxs)(C.gC,{children:[(0,l.jsx)(C.eb,{value:"5",children:"5"}),(0,l.jsx)(C.eb,{value:"10",children:"10"}),(0,l.jsx)(C.eb,{value:"20",children:"20"}),(0,l.jsx)(C.eb,{value:"50",children:"50"})]})]})]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(N.$,{variant:"outline",size:"sm",onClick:()=>ef(q-1),disabled:1===q,className:"border-orange-200 hover:bg-orange-50",children:"Previous"}),Array.from({length:Math.min(5,eg)},(e,s)=>{let a=Math.max(1,Math.min(eg-4,q-2))+s;return(0,l.jsx)(N.$,{variant:a===q?"default":"outline",size:"sm",onClick:()=>ef(a),className:"w-8 h-8 p-0 ".concat(a===q?"bg-orange-600 hover:bg-orange-700":"border-orange-200 hover:bg-orange-50"),children:a},a)}),(0,l.jsx)(N.$,{variant:"outline",size:"sm",onClick:()=>ef(q+1),disabled:q===eg,className:"border-orange-200 hover:bg-orange-50",children:"Next"})]})]})]})})]})]})}),(0,l.jsx)(k.lG,{open:ec,onOpenChange:ei,children:(0,l.jsxs)(k.Cf,{className:"sm:max-w-[500px]",children:[(0,l.jsx)(k.c7,{children:(0,l.jsx)(k.L3,{children:"Add New Case - Igatpuri"})}),(0,l.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"caseType",className:"text-right text-sm font-medium",children:"Case Type *"}),(0,l.jsxs)(C.l6,{value:ed.caseType,onValueChange:e=>eo(s=>({...s,caseType:e})),children:[(0,l.jsx)(C.bq,{className:"col-span-3",children:(0,l.jsx)(C.yv,{})}),(0,l.jsxs)(C.gC,{children:[(0,l.jsx)(C.eb,{value:"अपील",children:"अपील"}),(0,l.jsx)(C.eb,{value:"रिव्हीजन",children:"रिव्हीजन"}),(0,l.jsx)(C.eb,{value:"मामलेदार कोर्ट",children:"मामलेदार कोर्ट"}),(0,l.jsx)(C.eb,{value:"गौणखनिज",children:"गौणखनिज"}),(0,l.jsx)(C.eb,{value:"अतिक्रमण",children:"अतिक्रमण"}),(0,l.jsx)(C.eb,{value:"कुळ कायदा",children:"कुळ कायदा"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"caseNumber",className:"text-right text-sm font-medium",children:"Case Number *"}),(0,l.jsx)(v.p,{id:"caseNumber",value:ed.caseNumber,onChange:e=>eo(s=>({...s,caseNumber:e.target.value})),className:"col-span-3",placeholder:"e.g., अपील/150/2023"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"appellant",className:"text-right text-sm font-medium",children:"Appellant *"}),(0,l.jsx)(v.p,{id:"appellant",value:ed.appellant,onChange:e=>eo(s=>({...s,appellant:e.target.value})),className:"col-span-3",placeholder:"Appellant name"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"respondent",className:"text-right text-sm font-medium",children:"Respondent *"}),(0,l.jsx)(v.p,{id:"respondent",value:ed.respondent,onChange:e=>eo(s=>({...s,respondent:e.target.value})),className:"col-span-3",placeholder:"Respondent name"})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(N.$,{variant:"outline",onClick:()=>ei(!1),children:"Cancel"}),(0,l.jsx)(N.$,{onClick:()=>{if(!ed.caseNumber||!ed.appellant||!ed.respondent){alert("Please fill in all required fields");return}let e=O({date:new Date().toISOString().split("T")[0],caseType:ed.caseType,caseNumber:ed.caseNumber,appellant:ed.appellant,respondent:ed.respondent,received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Igatpuri",filedDate:new Date().toISOString().split("T")[0],lastUpdate:new Date().toISOString().split("T")[0]});e.success?(eo({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),ei(!1),alert("Case added successfully!")):alert("Failed to add case: ".concat(e.error))},className:"bg-orange-600 hover:bg-orange-700",children:"Add Case"})]})]})})]})}function I(){return(0,l.jsx)(E,{})}}},e=>{var s=s=>e(e.s=s);e.O(0,[863,822,352,441,684,358],()=>s(3947)),_N_E=e.O()}]);