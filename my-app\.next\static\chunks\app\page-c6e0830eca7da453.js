(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return s},formatWithValidation:function(){return i},urlObjectKeys:function(){return o}});let n=r(6966)._(r(8859)),a=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:r}=e,s=e.protocol||"",o=e.pathname||"",i=e.hash||"",l=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let u=e.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||a.test(s))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),u&&"?"!==u[0]&&(u="?"+u),""+s+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+i}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return s(e)}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(2596),a=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},5459:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var n=r(5155),a=r(6874),s=r.n(a),o=r(8482),i=r(7168),l=r(8686),c=r(9946);let u=(0,c.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),d=(0,c.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var f=r(4186);let p=(0,c.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var m=r(4516);let h=(0,c.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function g(){return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:(0,n.jsxs)("div",{className:"mx-auto max-w-6xl space-y-8 p-4 sm:p-6 lg:p-8",children:[(0,n.jsx)("div",{className:"text-center space-y-6 py-8 sm:py-12",children:(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4 mb-6",children:[(0,n.jsx)("div",{className:"p-4 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl shadow-lg",children:(0,n.jsx)(l.A,{className:"h-10 w-10 sm:h-12 sm:w-12 text-white"})}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Legal Case Monitoring System"}),(0,n.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 font-medium",children:"Sub-Divisional Magistrate Office, Nashik"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-500 max-w-2xl mx-auto",children:"Streamlined case management and monitoring for efficient legal proceedings across subdivisions"})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 mb-8",children:[(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(u,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Case Management"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(d,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Multi-User Access"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(f.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-orange-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Real-time Updates"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200",children:[(0,n.jsx)(p,{className:"h-6 w-6 sm:h-8 sm:w-8 text-purple-600 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-xs sm:text-sm font-medium text-gray-700",children:"Secure Platform"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8",children:[(0,n.jsxs)(o.Zp,{className:"group border border-orange-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-orange-50 to-amber-50",children:[(0,n.jsxs)(o.aR,{className:"text-center pb-6",children:[(0,n.jsx)("div",{className:"p-4 bg-gradient-to-br from-orange-500 to-amber-500 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,n.jsx)(m.A,{className:"h-8 w-8 text-white"})}),(0,n.jsx)(o.ZB,{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",children:"Igatpuri Subdivision"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-600 leading-relaxed",children:"Monitor and manage legal cases for Igatpuri area with comprehensive tracking and reporting"})]}),(0,n.jsx)(o.Wu,{className:"pt-0 pb-6",children:(0,n.jsx)(s(),{href:"/igatpuri",className:"block",children:(0,n.jsxs)(i.$,{className:"w-full bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",children:["Access Igatpuri Dashboard",(0,n.jsx)(h,{className:"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"})]})})})]}),(0,n.jsxs)(o.Zp,{className:"group border border-indigo-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-indigo-50 to-purple-50",children:[(0,n.jsxs)(o.aR,{className:"text-center pb-6",children:[(0,n.jsx)("div",{className:"p-4 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,n.jsx)(m.A,{className:"h-8 w-8 text-white"})}),(0,n.jsx)(o.ZB,{className:"text-xl sm:text-2xl font-bold text-gray-900 mb-2",children:"Trimbakeshwar Subdivision"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-600 leading-relaxed",children:"Monitor and manage legal cases for Trimbakeshwar area with comprehensive tracking and reporting"})]}),(0,n.jsx)(o.Wu,{className:"pt-0 pb-6",children:(0,n.jsx)(s(),{href:"/trimbakeshwar",className:"block",children:(0,n.jsxs)(i.$,{className:"w-full bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",children:["Access Trimbakeshwar Dashboard",(0,n.jsx)(h,{className:"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"})]})})})]})]}),(0,n.jsxs)("div",{className:"text-center py-8 border-t border-gray-200 mt-12",children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"\xa9 2024 Sub-Divisional Magistrate Office, Nashik. All rights reserved."}),(0,n.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Powered by Legal Case Monitoring System"})]})]})})}},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let n=r(2115);function a(e,t){let r=(0,n.useRef)(null),a=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(r.current=s(e,n)),t&&(a.current=s(t,n))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return m}});let n=r(8229),a=r(5155),s=n._(r(2115)),o=r(2757),i=r(5227),l=r(9818),c=r(6654),u=r(9991),d=r(5929);r(3230);let f=r(4930);function p(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}let m=s.default.forwardRef(function(e,t){let r,n;let{href:o,as:m,children:h,prefetch:g=null,passHref:x,replace:b,shallow:v,scroll:y,onClick:j,onMouseEnter:N,onTouchStart:w,legacyBehavior:k=!1,...P}=e;r=h,k&&("string"==typeof r||"number"==typeof r)&&(r=(0,a.jsx)("a",{children:r}));let _=s.default.useContext(i.AppRouterContext),M=!1!==g,E=null===g?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:O,as:A}=s.default.useMemo(()=>{let e=p(o);return{href:e,as:m?p(m):e}},[o,m]);k&&(n=s.default.Children.only(r));let S=k?n&&"object"==typeof n&&n.ref:t,C=s.default.useCallback(e=>(M&&null!==_&&(0,f.mountLinkInstance)(e,O,_,E),()=>{(0,f.unmountLinkInstance)(e)}),[M,O,_,E]),T={ref:(0,c.useMergedRef)(C,S),onClick(e){k||"function"!=typeof j||j(e),k&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),_&&!e.defaultPrevented&&!function(e,t,r,n,a,o,i){let{nodeName:l}=e.currentTarget;!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e))&&(e.preventDefault(),s.default.startTransition(()=>{let e=null==i||i;"beforePopState"in t?t[a?"replace":"push"](r,n,{shallow:o,scroll:e}):t[a?"replace":"push"](n||r,{scroll:e})}))}(e,_,O,A,b,v,y)},onMouseEnter(e){k||"function"!=typeof N||N(e),k&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),_&&M&&(0,f.onNavigationIntent)(e.currentTarget)},onTouchStart:function(e){k||"function"!=typeof w||w(e),k&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),_&&M&&(0,f.onNavigationIntent)(e.currentTarget)}};return(0,u.isAbsoluteUrl)(A)?T.href=A:k&&!x&&("a"!==n.type||"href"in n.props)||(T.href=(0,d.addBasePath)(A)),k?s.default.cloneElement(n,T):(0,a.jsx)("a",{...P,...T,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(5155);r(2115);var a=r(9708),s=r(2085),o=r(3999);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...c}=e,u=l?a.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:s,className:t})),...c})}},8482:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,ZB:()=>i,Zp:()=>s,aR:()=>o});var n=r(5155);r(2115);var a=r(3999);function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}},8859:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(r,n(e));else t.set(r,n(a));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},9310:(e,t,r)=>{Promise.resolve().then(r.bind(r,5459))},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return x},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return i},isAbsoluteUrl:function(){return s},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),s=0;s<n;s++)a[s]=arguments[s];return r||(r=!0,t=e(...a)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>a.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}}},e=>{var t=t=>e(e.s=t);e.O(0,[863,441,684,358],()=>t(9310)),_N_E=e.O()}]);