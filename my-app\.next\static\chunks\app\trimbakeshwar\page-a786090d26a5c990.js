(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[937],{5127:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var l=a(5155),t=a(2115),i=a(3453),n=a(1191),r=a(9074),c=a(4186),d=a(2486),o=a(3904),m=a(7550),x=a(8686),h=a(4516),p=a(7924),u=a(6932),g=a(1788),j=a(5213),b=a(5488),N=a(7168),v=a(9852),w=a(8482),f=a(8524),C=a(5784),y=a(8145),S=a(7777),k=a(7133),A=a(9840),T=a(9474),D=a(1886),L=a(7996);function E(){let{cases:e,loading:s,error:a,lastUpdated:E,updateCasesFromCsv:$,refreshCases:O,addCase:R}=(0,L.L)(),[F,M]=(0,t.useState)(""),[_,V]=(0,t.useState)("All Types"),[B,I]=(0,t.useState)("All Statuses"),[P,q]=(0,t.useState)(1),[z,U]=(0,t.useState)(null),[W,Z]=(0,t.useState)("asc"),[Y,H]=(0,t.useState)(10),[G,Q]=(0,t.useState)({}),[X,J]=(0,t.useState)({}),[K,ee]=(0,t.useState)({}),[es,ea]=(0,t.useState)({}),[el,et]=(0,t.useState)(""),[ei,en]=(0,t.useState)(null),[er,ec]=(0,t.useState)(!1),[ed,eo]=(0,t.useState)({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),em=(0,t.useMemo)(()=>e.filter(e=>"Trimbakeshwar"===e.taluka),[e]),ex=(0,t.useMemo)(()=>["All Types",...new Set(em.map(e=>e.caseType).filter(Boolean))],[em]),eh=(0,t.useMemo)(()=>["All Statuses",...new Set(em.map(e=>X[e.caseNumber]||e.received).filter(Boolean))],[em,X]),ep=new Date,eu=(0,t.useMemo)(()=>{let e=em.filter(e=>{var s,a,l,t;let i=(null===(s=e.caseNumber)||void 0===s?void 0:s.toLowerCase().includes(F.toLowerCase()))||(null===(a=e.appellant)||void 0===a?void 0:a.toLowerCase().includes(F.toLowerCase()))||(null===(l=e.respondent)||void 0===l?void 0:l.toLowerCase().includes(F.toLowerCase()))||(null===(t=e.caseType)||void 0===t?void 0:t.toLowerCase().includes(F.toLowerCase())),n="All Types"===_||e.caseType===_,r="All Statuses"===B||(X[e.caseNumber]||e.received)===B;return i&&n&&r});return z&&e.sort((e,s)=>{let a=e[z],l=s[z];return("date"===z&&(a=new Date(a).getTime(),l=new Date(l).getTime()),a<l)?"asc"===W?-1:1:a>l?"asc"===W?1:-1:0}),e},[em,F,_,B,z,W]),eg=Math.ceil(eu.length/Y),ej=(P-1)*Y,eb=eu.slice(ej,ej+Y),eN=(0,t.useMemo)(()=>{let e=eu.length;return{total:e,received:eu.filter(e=>"प्राप्त"===(X[e.caseNumber]||e.received)).length,nextDate:eu.filter(e=>""!==(K[e.caseNumber]||e.nextDate||"").trim()).length}},[eu,X,K]),ev=e=>{z===e?Z("asc"===W?"desc":"asc"):(U(e),Z("asc")),q(1)},ew=e=>{q(e)},ef=async(e,s)=>{Q(a=>({...a,[e]:s}));try{let a=await (0,D.Yp)(e,"status",s);a.success||console.error("Failed to update status:",a.error)}catch(e){console.error("Error updating status:",e)}},eC=async(e,s)=>{J(a=>({...a,[e]:s}));try{let a=await (0,D.Yp)(e,"received",s);a.success||console.error("Failed to update received status:",a.error)}catch(e){console.error("Error updating received status:",e)}},ey=async(e,s)=>{ee(a=>({...a,[e]:s}));try{let a=await (0,D.Yp)(e,"next_date",s);a.success||console.error("Failed to update next date:",a.error)}catch(e){console.error("Error updating next date:",e)}},eS=(e,s)=>{en(e),et(G[e]||s||""),ea(s=>({...s,[e]:!0}))},ek=e=>{ea(s=>({...s,[e]:!1})),en(null),et("")},eA=e=>{ef(e,el),ek(e)},eT=e=>{let s=eu.map(e=>({Date:e.date,"Case Type":e.caseType,"Case Number":e.caseNumber,Year:e.year,Appellant:e.appellant,Respondent:e.respondent,Received:X[e.caseNumber]||e.received||"-",Status:G[e.caseNumber]||e.status||""}));if("CSV"===e){let e=new Blob([[Object.keys(s[0]).join(","),...s.map(e=>Object.values(e).join(","))].join("\n")],{type:"text/csv"}),a=window.URL.createObjectURL(e),l=document.createElement("a");l.href=a,l.download="trimbakeshwar-cases-".concat(new Date().toISOString().split("T")[0],".csv"),l.click(),window.URL.revokeObjectURL(a)}else alert("".concat(e," export functionality would be implemented here"))},eD=e=>{let s=(null==e?void 0:e.toLowerCase())||"";if(s.includes("completed"))return{variant:"default",icon:i.A,color:"text-green-700"};if(s.includes("received"))return{variant:"secondary",icon:n.A,color:"text-indigo-700"};if(s.includes("scheduled"))return{variant:"outline",icon:r.A,color:"text-blue-700"};if(s.includes("review"))return{variant:"secondary",icon:c.A,color:"text-cyan-700"};if(s.includes("issued"))return{variant:"outline",icon:d.A,color:"text-purple-700"};else return{variant:"outline",icon:c.A,color:"text-gray-700"}};return s?(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50/20 to-blue-50/20 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-indigo-600"}),(0,l.jsx)("p",{className:"text-indigo-700",children:"Loading cases..."})]})}):(0,l.jsxs)(S.Bc,{children:[(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50/20 to-blue-50/20",children:(0,l.jsxs)("div",{className:"mx-auto max-w-7xl space-y-4 p-3 sm:p-4",children:[(0,l.jsxs)("div",{className:"text-center space-y-2 pb-4 border-b bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm border-indigo-100",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[(0,l.jsxs)(N.$,{variant:"ghost",size:"sm",onClick:()=>window.location.href="/",className:"flex items-center gap-2 text-indigo-700 hover:text-indigo-900 hover:bg-indigo-100 self-start sm:self-center",children:[(0,l.jsx)(m.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Back to Home"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg",children:(0,l.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,l.jsxs)("div",{className:"text-center sm:text-left",children:[(0,l.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-indigo-900",children:"Trimbakeshwar Legal Case Dashboard"}),(0,l.jsx)("p",{className:"text-xs sm:text-sm text-indigo-700",children:"Sub-Divisional Magistrate Office, Nashik"})]})]}),(0,l.jsx)("div",{className:"hidden sm:block w-24"})," "]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-6 text-xs text-indigo-600",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(h.A,{className:"h-3 w-3"}),(0,l.jsx)("span",{children:"Trimbakeshwar Subdivision"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(r.A,{className:"h-3 w-3"}),(0,l.jsx)("span",{children:ep.toLocaleDateString("en-IN",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4",children:[(0,l.jsx)(w.Zp,{className:"border border-indigo-100 shadow-sm bg-gradient-to-br from-indigo-50/50 to-white",children:(0,l.jsxs)(w.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,l.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-indigo-600 mb-1",children:eN.total}),(0,l.jsx)("div",{className:"text-xs text-indigo-700",children:"Total Cases"})]})}),(0,l.jsx)(w.Zp,{className:"border border-green-100 shadow-sm bg-gradient-to-br from-green-50/50 to-white",children:(0,l.jsxs)(w.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,l.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600 mb-1",children:eN.received}),(0,l.jsx)("div",{className:"text-xs text-green-700",children:"Received"})]})}),(0,l.jsx)(w.Zp,{className:"border border-blue-100 shadow-sm bg-gradient-to-br from-blue-50/50 to-white",children:(0,l.jsxs)(w.Wu,{className:"p-3 sm:p-4 text-center",children:[(0,l.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-blue-600 mb-1",children:eN.nextDate}),(0,l.jsx)("div",{className:"text-xs text-blue-700",children:"Next Date"})]})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(w.Zp,{className:"border border-indigo-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,l.jsxs)(w.Wu,{className:"p-4 space-y-4",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400 h-4 w-4"}),(0,l.jsx)(v.p,{placeholder:"Search cases...",value:F,onChange:e=>M(e.target.value),className:"pl-10 h-10 border-indigo-200 focus:border-indigo-500 bg-white/50"})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-wrap",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,l.jsxs)(C.l6,{value:_,onValueChange:V,children:[(0,l.jsx)(C.bq,{className:"h-9 text-sm border-indigo-200 bg-white/50 min-w-[120px]",children:(0,l.jsx)(C.yv,{placeholder:"Type"})}),(0,l.jsx)(C.gC,{children:ex.map(e=>(0,l.jsx)(C.eb,{value:e,children:e},e))})]}),(0,l.jsxs)(C.l6,{value:B,onValueChange:I,children:[(0,l.jsx)(C.bq,{className:"h-9 text-sm border-indigo-200 bg-white/50 min-w-[120px]",children:(0,l.jsx)(C.yv,{placeholder:"Status"})}),(0,l.jsx)(C.gC,{children:eh.map(e=>(0,l.jsx)(C.eb,{value:e,children:e},e))})]})]}),(0,l.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,l.jsxs)(N.$,{onClick:()=>ec(!0),className:"h-9 text-sm bg-indigo-600 hover:bg-indigo-700 text-white",children:[(0,l.jsx)("span",{className:"text-lg mr-1",children:"+"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Add New Case"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Add"})]}),(0,l.jsxs)(N.$,{variant:"outline",onClick:()=>{M(""),V("All Types"),I("All Statuses"),q(1),U(null)},className:"h-9 text-sm bg-white/50 border-indigo-200 hover:bg-indigo-50",children:[(0,l.jsx)(u.A,{className:"h-3 w-3 mr-1"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Clear"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Clear"})]}),(0,l.jsxs)(k.rI,{children:[(0,l.jsx)(k.ty,{asChild:!0,children:(0,l.jsxs)(N.$,{variant:"outline",className:"h-9 text-sm bg-white/50 border-indigo-200 hover:bg-indigo-50",children:[(0,l.jsx)(g.A,{className:"h-3 w-3 mr-1"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Export"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Export"})]})}),(0,l.jsxs)(k.SQ,{children:[(0,l.jsx)(k._2,{onClick:()=>eT("CSV"),children:"Export as CSV"}),(0,l.jsx)(k._2,{onClick:()=>eT("PDF"),children:"Export as PDF"}),(0,l.jsx)(k._2,{onClick:()=>eT("Excel"),children:"Export as Excel"})]})]}),(0,l.jsxs)(N.$,{variant:"outline",onClick:O,disabled:s,className:"h-9 text-sm bg-white/50 border-indigo-200 hover:bg-indigo-50",children:[(0,l.jsx)(o.A,{className:"h-3 w-3 mr-1 ".concat(s?"animate-spin":"")}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Refresh"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Refresh"})]})]})]}),(0,l.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 text-sm text-indigo-700",children:(0,l.jsxs)("span",{children:["Showing ",eb.length," of ",eu.length," cases"]})})]})}),(0,l.jsx)(w.Zp,{className:"border border-indigo-100 shadow-sm bg-white/80 backdrop-blur-sm",children:(0,l.jsxs)(w.Wu,{className:"p-0",children:[(0,l.jsx)("div",{className:"overflow-x-auto min-w-full",children:(0,l.jsxs)(f.XI,{className:"min-w-[800px]",children:[(0,l.jsx)(f.A0,{children:(0,l.jsxs)(f.Hj,{className:"border-b border-indigo-100",children:[(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[100px]",children:"Case Type"}),(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[140px]",children:(0,l.jsxs)(N.$,{variant:"ghost",onClick:()=>ev("caseNumber"),className:"h-auto p-0 font-semibold hover:bg-indigo-50",children:["Case Number","caseNumber"===z&&("asc"===W?(0,l.jsx)(j.A,{className:"ml-1 h-3 w-3"}):(0,l.jsx)(b.A,{className:"ml-1 h-3 w-3"}))]})}),(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[120px]",children:"Appellant"}),(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[120px]",children:"Respondent"}),(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[80px]",children:"Received"}),(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[140px]",children:"Next Date"}),(0,l.jsx)(f.nd,{className:"font-semibold text-indigo-900 min-w-[120px]",children:"Status"})]})}),(0,l.jsx)(f.BF,{children:eb.map((e,s)=>(eD(e.status).icon,(0,l.jsxs)(f.Hj,{className:"hover:bg-indigo-50/50",children:[(0,l.jsx)(f.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)(y.E,{variant:"outline",className:"text-xs border-indigo-200 text-indigo-700 whitespace-nowrap",children:e.caseType})}),(0,l.jsx)(f.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)("div",{className:"font-semibold text-sm break-all",children:e.caseNumber})}),(0,l.jsx)(f.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)("div",{className:"font-medium text-sm break-words",children:e.appellant})}),(0,l.jsx)(f.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)("div",{className:"text-sm break-words",children:e.respondent})}),(0,l.jsx)(f.nA,{className:"p-2 sm:p-4",children:(0,l.jsxs)(C.l6,{value:X[e.caseNumber]||e.received||"-",onValueChange:s=>eC(e.caseNumber,s),children:[(0,l.jsx)(C.bq,{className:"w-full min-w-[70px] h-8 text-xs border-indigo-200",children:(0,l.jsx)(C.yv,{})}),(0,l.jsxs)(C.gC,{children:[(0,l.jsx)(C.eb,{value:"प्राप्त",children:"प्राप्त"}),(0,l.jsx)(C.eb,{value:"-",children:"-"})]})]})}),(0,l.jsx)(f.nA,{className:"p-2 sm:p-4",children:(0,l.jsx)(v.p,{type:"date",value:K[e.caseNumber]||e.nextDate||"2025-07-17",onChange:s=>ey(e.caseNumber,s.target.value),className:"w-full min-w-[130px] h-8 text-xs border-indigo-200 focus:border-indigo-400"})}),(0,l.jsx)(f.nA,{children:(0,l.jsxs)(A.lG,{open:es[e.caseNumber]||!1,onOpenChange:s=>{s||ek(e.caseNumber)},children:[(0,l.jsx)(A.zM,{asChild:!0,children:(0,l.jsxs)(N.$,{variant:"outline",size:"sm",className:"text-xs h-8 min-w-[120px] justify-start",onClick:()=>eS(e.caseNumber,e.status),children:[(G[e.caseNumber]||e.status||"Enter status...").substring(0,15),(G[e.caseNumber]||e.status||"").length>15?"...":""]})}),(0,l.jsxs)(A.Cf,{className:"sm:max-w-[425px]",children:[(0,l.jsx)(A.c7,{children:(0,l.jsxs)(A.L3,{children:["Edit Status - Case #",e.caseNumber]})}),(0,l.jsx)("div",{className:"grid gap-4 py-4",children:(0,l.jsx)(T.T,{value:el,onChange:e=>et(e.target.value),placeholder:"Enter detailed status information...",className:"min-h-[100px]"})}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(N.$,{variant:"outline",onClick:()=>ek(e.caseNumber),children:"Cancel"}),(0,l.jsx)(N.$,{onClick:()=>eA(e.caseNumber),children:"Save"})]})]})]})})]},"".concat(e.caseNumber,"-").concat(s))))})]})}),eg>1&&(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-3 p-4 border-t border-indigo-100",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)("span",{className:"text-sm text-indigo-700",children:["Page ",P," of ",eg]}),(0,l.jsxs)(C.l6,{value:Y.toString(),onValueChange:e=>{H(Number(e)),q(1)},children:[(0,l.jsx)(C.bq,{className:"w-20 h-8 border-indigo-200",children:(0,l.jsx)(C.yv,{})}),(0,l.jsxs)(C.gC,{children:[(0,l.jsx)(C.eb,{value:"5",children:"5"}),(0,l.jsx)(C.eb,{value:"10",children:"10"}),(0,l.jsx)(C.eb,{value:"20",children:"20"}),(0,l.jsx)(C.eb,{value:"50",children:"50"})]})]})]}),(0,l.jsxs)("div",{className:"flex gap-1 sm:gap-2",children:[(0,l.jsxs)(N.$,{variant:"outline",size:"sm",onClick:()=>ew(P-1),disabled:1===P,className:"border-indigo-200 hover:bg-indigo-50 text-xs sm:text-sm px-2 sm:px-3",children:[(0,l.jsx)("span",{className:"hidden sm:inline",children:"Previous"}),(0,l.jsx)("span",{className:"sm:hidden",children:"Prev"})]}),Array.from({length:Math.min(5,eg)},(e,s)=>{let a=Math.max(1,Math.min(eg-4,P-2))+s;return(0,l.jsx)(N.$,{variant:a===P?"default":"outline",size:"sm",onClick:()=>ew(a),className:"w-7 h-7 sm:w-8 sm:h-8 p-0 text-xs sm:text-sm ".concat(a===P?"bg-indigo-600 hover:bg-indigo-700":"border-indigo-200 hover:bg-indigo-50"),children:a},a)}),(0,l.jsx)(N.$,{variant:"outline",size:"sm",onClick:()=>ew(P+1),disabled:P===eg,className:"border-indigo-200 hover:bg-indigo-50 text-xs sm:text-sm px-2 sm:px-3",children:"Next"})]})]})]})})]})]})}),(0,l.jsx)(A.lG,{open:er,onOpenChange:ec,children:(0,l.jsxs)(A.Cf,{className:"sm:max-w-[500px]",children:[(0,l.jsx)(A.c7,{children:(0,l.jsx)(A.L3,{children:"Add New Case - Trimbakeshwar"})}),(0,l.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"caseType",className:"text-right text-sm font-medium",children:"Case Type *"}),(0,l.jsxs)(C.l6,{value:ed.caseType,onValueChange:e=>eo(s=>({...s,caseType:e})),children:[(0,l.jsx)(C.bq,{className:"col-span-3",children:(0,l.jsx)(C.yv,{})}),(0,l.jsxs)(C.gC,{children:[(0,l.jsx)(C.eb,{value:"अपील",children:"अपील"}),(0,l.jsx)(C.eb,{value:"रिव्हीजन",children:"रिव्हीजन"}),(0,l.jsx)(C.eb,{value:"मामलेदार कोर्ट",children:"मामलेदार कोर्ट"}),(0,l.jsx)(C.eb,{value:"गौणखनिज",children:"गौणखनिज"}),(0,l.jsx)(C.eb,{value:"अतिक्रमण",children:"अतिक्रमण"}),(0,l.jsx)(C.eb,{value:"कुळ कायदा",children:"कुळ कायदा"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"caseNumber",className:"text-right text-sm font-medium",children:"Case Number *"}),(0,l.jsx)(v.p,{id:"caseNumber",value:ed.caseNumber,onChange:e=>eo(s=>({...s,caseNumber:e.target.value})),className:"col-span-3",placeholder:"e.g., अपील/150/2023"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"appellant",className:"text-right text-sm font-medium",children:"Appellant *"}),(0,l.jsx)(v.p,{id:"appellant",value:ed.appellant,onChange:e=>eo(s=>({...s,appellant:e.target.value})),className:"col-span-3",placeholder:"Appellant name"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,l.jsx)("label",{htmlFor:"respondent",className:"text-right text-sm font-medium",children:"Respondent *"}),(0,l.jsx)(v.p,{id:"respondent",value:ed.respondent,onChange:e=>eo(s=>({...s,respondent:e.target.value})),className:"col-span-3",placeholder:"Respondent name"})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(N.$,{variant:"outline",onClick:()=>ec(!1),children:"Cancel"}),(0,l.jsx)(N.$,{onClick:()=>{if(!ed.caseNumber||!ed.appellant||!ed.respondent){alert("Please fill in all required fields");return}let e=R({date:new Date().toISOString().split("T")[0],caseType:ed.caseType,caseNumber:ed.caseNumber,appellant:ed.appellant,respondent:ed.respondent,received:"प्राप्त",nextDate:"2025-07-17",status:"",taluka:"Trimbakeshwar",filedDate:new Date().toISOString().split("T")[0],lastUpdate:new Date().toISOString().split("T")[0]});e.success?(eo({caseNumber:"",appellant:"",respondent:"",caseType:"अपील"}),ec(!1),alert("Case added successfully!")):alert("Failed to add case: ".concat(e.error))},className:"bg-indigo-600 hover:bg-indigo-700",children:"Add Case"})]})]})})]})}function $(){return(0,l.jsx)(E,{})}},8557:(e,s,a)=>{Promise.resolve().then(a.bind(a,5127))}},e=>{var s=s=>e(e.s=s);e.O(0,[863,822,352,441,684,358],()=>s(8557)),_N_E=e.O()}]);